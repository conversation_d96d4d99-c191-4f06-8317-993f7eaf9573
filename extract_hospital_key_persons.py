#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取潍坊市中医院关键人信息
"""

import docx
import sys
import os

def extract_key_persons():
    doc_path = "0715  战客动态2家/潍坊市中医院-战客动态.docx"
    
    if not os.path.exists(doc_path):
        print(f"文件不存在: {doc_path}")
        return
    
    try:
        doc = docx.Document(doc_path)
        
        print("=== 潍坊市中医院关键人信息提取 ===")
        print()
        
        # 提取所有段落
        paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                paragraphs.append({
                    'index': i + 1,
                    'text': text
                })
        
        print(f"总段落数: {len(paragraphs)}")
        print()
        
        # 查找关键人相关信息
        print("=== 关键人相关段落 ===")
        key_person_paragraphs = []
        
        for para in paragraphs:
            text = para['text']
            if any(keyword in text for keyword in ['院长', '副院长', '书记', '主任', '关键人', '负责人', '对接人', '联系方式', '分管', '主持']):
                key_person_paragraphs.append(para)
                print(f"{para['index']:3d}: {text}")
        
        print()
        print("=== 具体人员信息分析 ===")
        
        # 分析人员信息
        persons = []
        
        for para in key_person_paragraphs:
            text = para['text']
            
            # 查找包含姓名和职务的段落
            if '：' in text and any(title in text for title in ['院长', '副院长', '书记', '主任']):
                # 分析格式：姓名：职务，分工描述
                parts = text.split('：')
                if len(parts) >= 2:
                    name = parts[0].strip()
                    rest = '：'.join(parts[1:]).strip()
                    
                    # 进一步分析职务和描述
                    if '，' in rest:
                        title_desc = rest.split('，')
                        title = title_desc[0].strip()
                        desc = '，'.join(title_desc[1:]).strip()
                    else:
                        title = rest
                        desc = ""
                    
                    persons.append({
                        'name': name,
                        'title': title,
                        'desc': desc,
                        'source_line': para['index']
                    })
                    
                    print(f"姓名: {name}")
                    print(f"职务: {title}")
                    print(f"描述: {desc}")
                    print(f"来源行: {para['index']}")
                    print("---")
        
        print()
        print(f"=== 提取到的人员信息总数: {len(persons)} ===")
        
        for i, person in enumerate(persons, 1):
            print(f"{i}. {person['name']} - {person['title']}")
            if person['desc']:
                print(f"   {person['desc']}")
        
        # 查找其他相关信息
        print()
        print("=== 其他相关信息 ===")
        
        for para in paragraphs:
            text = para['text']
            if any(keyword in text for keyword in ['实控人', '社会角色', '母公司', '对接人', '联系方式']) and '：' in text:
                print(f"{para['index']:3d}: {text}")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_key_persons()
