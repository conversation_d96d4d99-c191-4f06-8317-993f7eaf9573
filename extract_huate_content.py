#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取山东华特磁电科技股份有限公司文档内容
"""

import docx
import sys
import os

def extract_document_content():
    doc_path = "0716 战客动态3家/山东华特磁电科技股份有限公司-战客动态.docx"
    
    if not os.path.exists(doc_path):
        print(f"文件不存在: {doc_path}")
        return
    
    try:
        doc = docx.Document(doc_path)
        
        print("=== 山东华特磁电科技股份有限公司文档内容提取 ===")
        print()
        
        # 提取所有段落
        paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                paragraphs.append({
                    'index': i + 1,
                    'text': text
                })
        
        print(f"总段落数: {len(paragraphs)}")
        print()
        
        # 输出前50个段落
        print("=== 前50个段落内容 ===")
        for para in paragraphs[:50]:
            print(f"{para['index']:3d}: {para['text']}")
        
        print()
        print("=== 表格内容 ===")
        
        # 提取表格
        for i, table in enumerate(doc.tables):
            print(f"\n表格 {i+1} ({len(table.rows)}行 x {len(table.columns) if table.rows else 0}列):")
            for j, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_data.append(cell_text)
                print(f"  行{j+1}: {' | '.join(row_data)}")
        
        print()
        print("=== 基础信息查找 ===")
        
        # 查找基础信息
        basic_keywords = ['企业名称', '成立日期', '企业属性', '分支机构']
        
        for keyword in basic_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")
        
        print()
        print("=== 价值评级查找 ===")
        
        # 查找价值评级
        rating_keywords = ['品牌价值', '收益价值', '合作紧密度', '★']
        
        for keyword in rating_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")
        
        print()
        print("=== 关键人查找 ===")
        
        # 查找关键人
        person_keywords = ['董事长', '总经理', '副总', '总裁', '经理', '主管', '关键人']
        
        for keyword in person_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_document_content()
