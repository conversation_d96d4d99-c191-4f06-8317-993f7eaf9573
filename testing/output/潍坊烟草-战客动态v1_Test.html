<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>山东潍坊烟草有限公司 - 企业画像报告</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #1d1d1f;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            min-width: 1200px;
            margin: 20px auto 30px;
            padding: 0 30px 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            margin: 0 -30px 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .navigation {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .nav-btn {
            background: #4a90a4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .nav-btn:hover {
            background: #6b73a0;
            transform: translateY(-2px);
        }
        
        .nav-btn.active {
            background: #8b5a9c;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.8em;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #4a90a4;
        }
        
        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 24px;
            margin-bottom: 20px;
            border: 1px solid #f0f0f0;
        }
        
        .card-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 1em;
            color: #1d1d1f;
            font-weight: 500;
        }
        
        .btn {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 2px;
        }
        
        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table th {
            background: #4a90a4;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9em;
        }
        
        .table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9em;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .score-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.8em;
        }
        
        .score-high { background: #d4edda; color: #155724; }
        .score-medium { background: #fff3cd; color: #856404; }
        .score-low { background: #f8d7da; color: #721c24; }
        
        @media (max-width: 1024px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
                min-width: auto;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .navigation {
                justify-content: flex-start;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        
        <div class="header">
            <h1>山东潍坊烟草有限公司</h1>
            <p>企业画像报告 · 生成时间: 2025年07月15日</p>
        </div>
        <div class="navigation"><button class="nav-btn" onclick="showSection('section1')">一、战客动态</button><button class="nav-btn" onclick="showSection('section2')">二、政策信息</button><button class="nav-btn" onclick="showSection('section3')">三、行业热点</button><button class="nav-btn" onclick="showSection('section4')">四、招投标信息</button><button class="nav-btn" onclick="showSection('section5')">五、商机类型</button></div>
        
        <div id="section1" class="section">
            <div class="section-title">一、战客动态</div>
            <div class="card">
                <div class="card-title">基础信息</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">企业名称</div>
                        <div class="info-value">企业名称（简称）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">成立日期</div>
                        <div class="info-value">未知</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">企业属性</div>
                        <div class="info-value">企业属性</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">经营地址</div>
                        <div class="info-value">经营地址</div>
                    </div>
                </div>
            </div>
        </div>
        <div id="section2" class="section" style="display: none;">
            <div class="section-title">二、政策信息</div>
            <div class="card">
                <div class="card-title">潍坊市潍城区烟草专卖局关于印发《烟草制品零售点布局规定》的通知</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">文号</div>
                        <div class="info-value">潍城烟法〔2023〕1号</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布单位</div>
                        <div class="info-value">潍坊市潍城区烟草专卖局</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布日期</div>
                        <div class="info-value">2023年11月27日</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">政策类型</div>
                        <div class="info-value"><span class="btn btn-info">地方性管理规范</span></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">政策链接</div>
                        <div class="info-value">
                            <a href="http://www.weicheng.gov.cn/WCQZWGK/xzgfxwj/11sdwy20227xxjs3/223285.html" target="_blank" 
                               style="color: #4a90a4; text-decoration: none; font-weight: 500;">
                                查看政策详情 →
                            </a>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                    <strong>主要内容：</strong>
                </div>
            </div>
            <div class="card">
                <div class="card-title">国家烟草专卖局关于修订印发《电子烟交易管理细则》的通知​</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">文号</div>
                        <div class="info-value">未披露</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布单位</div>
                        <div class="info-value">国家烟草专卖局</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布日期</div>
                        <div class="info-value">2024年8月27日</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">政策类型</div>
                        <div class="info-value"><span class="btn btn-info">行业监管规范</span></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">政策链接</div>
                        <div class="info-value">
                            <a href="https://www.gov.cn/gongbao/2024/issue_11626/202410/content_6978627.html" target="_blank" 
                               style="color: #4a90a4; text-decoration: none; font-weight: 500;">
                                查看政策详情 →
                            </a>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                    <strong>主要内容：</strong>
                </div>
            </div>
            <div class="card">
                <div class="card-title">国务院关于修改《中华人民共和国烟草专卖实施条例》的决定​</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">文号</div>
                        <div class="info-value">国务院令第750号</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布单位</div>
                        <div class="info-value">国务院</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布日期</div>
                        <div class="info-value">2021年11月10日</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">政策类型</div>
                        <div class="info-value"><span class="btn btn-info">行政法规</span></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">政策链接</div>
                        <div class="info-value">
                            <a href="https://www.gov.cn/zhengce/content/2021-11/26/content_5653631.htm" target="_blank" 
                               style="color: #4a90a4; text-decoration: none; font-weight: 500;">
                                查看政策详情 →
                            </a>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                    <strong>主要内容：</strong>
                </div>
            </div></div>
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>
                <div class="card">
                    <p>暂无行业热点信息</p>
                </div>
            </div>
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="card">
                    <p>暂无招投标信息</p>
                </div>
            </div>
        <div id="section5" class="section" style="display: none;">
            <div class="section-title">五、商机类型</div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>商机领域</th>
                            <th>潜在解决方案</th>
                            <th>客户价值</th>
                            <th>实施难度</th>
                            <th>商机评分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>​​智慧农业与烟叶供应链​​</td>
                            <td>5G+物联网平台（土壤墒情监测、无人机巡检）、区块链溯源系统</td>
                            <td>提升烟叶品质管控效率，降低种植成本20%</td>
                            <td>中高（需跨烟站部署）</td>
                            <td><span class="score-badge score-high">0.85</span></td>
                        </tr>
                        <tr>
                            <td>​​全渠道数字化营销​​</td>
                            <td>企业微信+小程序商城、AR产品展示、消费者大数据分析</td>
                            <td>拓展线上销售渠道，增强品牌黏性，预估增量营收15%</td>
                            <td>中（需对接现有ERP）</td>
                            <td><span class="score-badge score-medium">0.75</span></td>
                        </tr>
                        <tr>
                            <td>​​智慧园区与后勤管理​​</td>
                            <td>智慧食堂（刷脸支付、营养分析）、能耗监控系统、安防AI巡检</td>
                            <td>降低后勤运营成本30%，提升员工满意度</td>
                            <td>低（模块化部署）</td>
                            <td><span class="score-badge score-medium">0.70</span></td>
                        </tr>
                        <tr>
                            <td>​​业财融合与风控中台​​</td>
                            <td>移动端BI报表、RPA流程自动化、供应链金融平台</td>
                            <td>实现实时财务预警，缩短回款周期25%</td>
                            <td>高（需深度系统集成）</td>
                            <td><span class="score-badge score-high">0.80</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        function showSection(sectionId) {
            // 隐藏所有章节
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });
            
            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.nav-btn');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
            }
            
            // 添加active类到点击的按钮
            event.target.classList.add('active');
        }
        
        // 页面加载时显示第一个章节
        document.addEventListener('DOMContentLoaded', function() {
            showSection('section1');
            document.querySelector('.nav-btn').classList.add('active');
        });
    </script>
</body>
</html>