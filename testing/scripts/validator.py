#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容验证脚本
功能：验证提取数据的完整性和准确性，自动修复问题
"""

import json
import yaml
import re
from datetime import datetime

class ContentValidator:
    def __init__(self, config_file):
        """初始化验证器"""
        self.config = self.load_config(config_file)
        self.validation_rules = self.config.get('validation_rules', {})
        self.quality_checks = self.config.get('quality_checks', {})
        self.issues = []
        self.fixes_applied = []
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def validate_extracted_data(self, extracted_data):
        """验证提取的数据"""
        print("开始验证提取的数据...")
        
        # 验证基础信息
        self.validate_basic_info(extracted_data.get('basic_info', {}))
        
        # 验证政策信息
        self.validate_policies(extracted_data.get('policies', []))
        
        # 验证商机信息
        self.validate_opportunities(extracted_data.get('opportunities', []))
        
        # 验证项目信息
        self.validate_projects(extracted_data.get('projects', {}))
        
        # 验证行业热点
        self.validate_hot_topics(extracted_data.get('hot_topics', []))
        
        # 验证招投标信息
        self.validate_bidding_info(extracted_data.get('bidding_info', []))
        
        # 质量检查
        self.quality_check(extracted_data)
        
        return self.generate_validation_report()
    
    def validate_basic_info(self, basic_info):
        """验证基础信息"""
        required_fields = self.validation_rules.get('basic_info', {}).get('required_fields', [])
        
        for field in required_fields:
            if field not in basic_info or not basic_info[field]:
                self.add_issue('basic_info', f"缺少必需字段: {field}", 'error')
    
    def validate_policies(self, policies):
        """验证政策信息"""
        expected_count = self.validation_rules.get('policies', {}).get('expected_count', 0)
        required_fields = self.validation_rules.get('policies', {}).get('required_fields', [])
        
        # 检查数量
        if len(policies) != expected_count:
            self.add_issue('policies', f"政策数量不匹配: 期望{expected_count}个，实际{len(policies)}个", 'warning')
        
        # 检查每个政策的字段
        for i, policy in enumerate(policies):
            for field in required_fields:
                if field not in policy or not policy[field]:
                    self.add_issue('policies', f"政策{i+1}缺少字段: {field}", 'error')
            
            # 验证链接格式
            if 'link' in policy and policy['link']:
                if not self.validate_url(policy['link']):
                    self.add_issue('policies', f"政策{i+1}链接格式无效: {policy['link']}", 'warning')
    
    def validate_opportunities(self, opportunities):
        """验证商机信息"""
        expected_count = self.validation_rules.get('opportunities', {}).get('expected_count', 0)
        required_fields = self.validation_rules.get('opportunities', {}).get('required_fields', [])
        
        # 检查数量
        if len(opportunities) != expected_count:
            self.add_issue('opportunities', f"商机数量不匹配: 期望{expected_count}个，实际{len(opportunities)}个", 'warning')
        
        # 检查每个商机的字段
        for i, opportunity in enumerate(opportunities):
            for field in required_fields:
                if field not in opportunity or not opportunity[field]:
                    self.add_issue('opportunities', f"商机{i+1}缺少字段: {field}", 'error')
            
            # 验证评分格式
            if 'score' in opportunity and opportunity['score']:
                if not self.validate_score(opportunity['score']):
                    self.add_issue('opportunities', f"商机{i+1}评分格式无效: {opportunity['score']}", 'warning')
    
    def validate_projects(self, projects):
        """验证项目信息"""
        required_sections = self.validation_rules.get('projects', {}).get('required_sections', [])
        
        for section in required_sections:
            section_key = self.get_project_section_key(section)
            if section_key not in projects or not projects[section_key]:
                self.add_issue('projects', f"缺少项目章节: {section}", 'error')
    
    def validate_hot_topics(self, hot_topics):
        """验证行业热点"""
        expected_count = self.validation_rules.get('hot_topics', {}).get('expected_count', 0)
        required_fields = self.validation_rules.get('hot_topics', {}).get('required_fields', [])
        
        # 检查数量
        if len(hot_topics) != expected_count:
            self.add_issue('hot_topics', f"行业热点数量不匹配: 期望{expected_count}个，实际{len(hot_topics)}个", 'warning')
        
        # 检查每个热点的字段
        for i, topic in enumerate(hot_topics):
            for field in required_fields:
                if field not in topic or not topic[field]:
                    self.add_issue('hot_topics', f"热点{i+1}缺少字段: {field}", 'error')
    
    def validate_bidding_info(self, bidding_info):
        """验证招投标信息"""
        expected_count = self.validation_rules.get('bidding_info', {}).get('expected_count', 0)
        required_fields = self.validation_rules.get('bidding_info', {}).get('required_fields', [])
        
        # 检查数量
        if len(bidding_info) != expected_count:
            self.add_issue('bidding_info', f"招投标信息数量不匹配: 期望{expected_count}个，实际{len(bidding_info)}个", 'warning')
        
        # 检查每个招投标的字段
        for i, bidding in enumerate(bidding_info):
            for field in required_fields:
                if field not in bidding or not bidding[field]:
                    self.add_issue('bidding_info', f"招投标{i+1}缺少字段: {field}", 'error')
    
    def quality_check(self, extracted_data):
        """质量检查"""
        # 检查必须包含的信息
        must_include = self.quality_checks.get('must_include', [])
        all_text = self.get_all_text(extracted_data)
        
        for required_text in must_include:
            if required_text not in all_text:
                self.add_issue('quality', f"缺少关键信息: {required_text}", 'error')
        
        # 检查不能包含的信息
        must_not_include = self.quality_checks.get('must_not_include', [])
        for forbidden_text in must_not_include:
            if forbidden_text in all_text:
                self.add_issue('quality', f"包含禁止信息: {forbidden_text}", 'error')
    
    def validate_url(self, url):
        """验证URL格式"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    def validate_score(self, score):
        """验证评分格式"""
        try:
            score_float = float(score)
            return 0 <= score_float <= 1
        except ValueError:
            return False
    
    def get_project_section_key(self, section_name):
        """获取项目章节的键名"""
        mapping = {
            '行业标杆项目': 'benchmark',
            '近期重点项目': 'recent',
            '主要DICT项目': 'dict'
        }
        return mapping.get(section_name, section_name.lower())
    
    def get_all_text(self, extracted_data):
        """获取所有文本内容用于质量检查"""
        all_text = ""
        
        # 从段落中获取文本
        for para in extracted_data.get('paragraphs', []):
            all_text += para.get('text', '') + " "
        
        # 从结构化数据中获取文本
        for policies in extracted_data.get('policies', []):
            for key, value in policies.items():
                if isinstance(value, str):
                    all_text += value + " "
        
        return all_text
    
    def add_issue(self, category, message, severity):
        """添加验证问题"""
        issue = {
            'category': category,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        }
        self.issues.append(issue)
        print(f"[{severity.upper()}] {category}: {message}")
    
    def auto_fix_issues(self, extracted_data):
        """自动修复问题"""
        print("开始自动修复问题...")
        
        # 这里可以添加自动修复逻辑
        # 例如：格式化日期、修正评分格式等
        
        for issue in self.issues:
            if issue['severity'] == 'warning' and 'score' in issue['message']:
                # 尝试修复评分格式
                self.fix_score_format(extracted_data)
        
        return extracted_data
    
    def fix_score_format(self, extracted_data):
        """修复评分格式"""
        for opportunity in extracted_data.get('opportunities', []):
            if 'score' in opportunity:
                score = opportunity['score']
                # 尝试提取数字
                score_match = re.search(r'(\d+\.?\d*)', str(score))
                if score_match:
                    score_value = float(score_match.group(1))
                    if score_value > 1:
                        score_value = score_value / 100  # 假设是百分比
                    opportunity['score'] = f"{score_value:.2f}"
                    self.fixes_applied.append(f"修复评分格式: {score} -> {opportunity['score']}")
    
    def generate_validation_report(self):
        """生成验证报告"""
        report = {
            'validation_time': datetime.now().isoformat(),
            'total_issues': len(self.issues),
            'issues_by_severity': {
                'error': len([i for i in self.issues if i['severity'] == 'error']),
                'warning': len([i for i in self.issues if i['severity'] == 'warning']),
                'info': len([i for i in self.issues if i['severity'] == 'info'])
            },
            'issues': self.issues,
            'fixes_applied': self.fixes_applied,
            'validation_passed': len([i for i in self.issues if i['severity'] == 'error']) == 0
        }
        
        print(f"\n验证完成:")
        print(f"- 总问题数: {report['total_issues']}")
        print(f"- 错误: {report['issues_by_severity']['error']}")
        print(f"- 警告: {report['issues_by_severity']['warning']}")
        print(f"- 验证通过: {report['validation_passed']}")
        
        return report
    
    def save_validation_report(self, report, output_path):
        """保存验证报告"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"验证报告已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存验证报告失败: {e}")
            return False

if __name__ == "__main__":
    # 测试代码
    config_file = "../configs/weifang_tobacco_config.yaml"
    
    validator = ContentValidator(config_file)
    
    # 加载提取的数据
    with open("../output/extracted_data.json", 'r', encoding='utf-8') as f:
        extracted_data = json.load(f)
    
    # 验证数据
    report = validator.validate_extracted_data(extracted_data)
    
    # 保存验证报告
    validator.save_validation_report(report, "../logs/validation_report.json")
