#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取潍坊市中医院文档内容
严格按照AI report配置v1.md强制性原文档读取流程
"""

import docx
import sys
import os

def extract_document_content():
    doc_path = "0715  战客动态2家/潍坊市中医院-战客动态.docx"
    
    if not os.path.exists(doc_path):
        print(f"文件不存在: {doc_path}")
        return
    
    try:
        doc = docx.Document(doc_path)
        
        print("=== 潍坊市中医院文档内容提取 ===")
        print()
        
        # 提取所有段落
        paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                paragraphs.append({
                    'index': i + 1,
                    'text': text
                })
        
        print(f"总段落数: {len(paragraphs)}")
        print()
        
        # 输出所有段落内容
        print("=== 完整段落内容 ===")
        for para in paragraphs:
            print(f"{para['index']:3d}: {para['text']}")
        
        print()
        print("=== 表格内容 ===")
        
        # 提取表格
        for i, table in enumerate(doc.tables):
            print(f"\n表格 {i+1} ({len(table.rows)}行 x {len(table.columns) if table.rows else 0}列):")
            for j, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_data.append(cell_text)
                print(f"  行{j+1}: {' | '.join(row_data)}")
        
        print()
        print("=== 基础信息强制验证 ===")
        
        # 基础信息强制验证
        basic_keywords = ['企业名称', '成立日期', '注册资金', '经营地址', '企业属性', '实缴资金', '隶属关系', '分支机构']
        
        for keyword in basic_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")
        
        print()
        print("=== 价值度评级强制验证 ===")
        
        # 价值度评级强制验证
        rating_keywords = ['价值评级', '品牌价值', '收益价值', '合作紧密度', '★', '星']
        
        for keyword in rating_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")
        
        print()
        print("=== 商机表格强制验证 ===")
        
        # 商机表格强制验证
        if len(doc.tables) > 0:
            for i, table in enumerate(doc.tables):
                print(f"\n分析表格{i+1}:")
                if len(table.rows) > 0:
                    headers = [cell.text.strip() for cell in table.rows[0].cells]
                    print(f"  表头: {' | '.join(headers)}")
                    
                    # 判断是否为商机表格
                    header_text = ' '.join(headers)
                    if any(keyword in header_text for keyword in ['商机', '场景', '方案', '痛点', '竞争', '评分']):
                        print(f"  -> 识别为商机表格")
                        print(f"  -> 总行数: {len(table.rows)}行（包含表头）")
                        print(f"  -> 数据行数: {len(table.rows)-1}行")
                        
                        # 输出所有数据行
                        for j, row in enumerate(table.rows[1:], 1):
                            row_data = [cell.text.strip() for cell in row.cells]
                            print(f"    数据行{j}: {' | '.join(row_data)}")
        else:
            print("  未找到任何表格")
        
        print()
        print("=== 关键信息统计 ===")
        
        # 统计关键信息
        stats = {
            '基础信息项目': 0,
            '价值评级项目': 0,
            '荣誉奖项数量': 0,
            '商机表格行数': 0,
            '政策文件数量': 0,
            '关键人员数量': 0
        }
        
        # 统计基础信息
        for keyword in basic_keywords:
            for para in paragraphs:
                if keyword in para['text']:
                    stats['基础信息项目'] += 1
                    break
        
        # 统计价值评级
        for keyword in ['品牌价值', '收益价值', '合作紧密度']:
            for para in paragraphs:
                if keyword in para['text']:
                    stats['价值评级项目'] += 1
                    break
        
        # 统计荣誉奖项
        for para in paragraphs:
            if any(keyword in para['text'] for keyword in ['荣誉', '奖项', '获得', '认定']):
                stats['荣誉奖项数量'] += 1
        
        # 统计商机表格
        for table in doc.tables:
            if len(table.rows) > 1:
                headers = [cell.text.strip() for cell in table.rows[0].cells]
                header_text = ' '.join(headers)
                if any(keyword in header_text for keyword in ['商机', '场景', '方案', '痛点', '竞争', '评分']):
                    stats['商机表格行数'] = len(table.rows) - 1
                    break
        
        # 统计政策文件
        for para in paragraphs:
            if any(keyword in para['text'] for keyword in ['政策', '文件', '通知', '规定', '办法']):
                stats['政策文件数量'] += 1
        
        # 统计关键人员
        for para in paragraphs:
            if any(keyword in para['text'] for keyword in ['院长', '副院长', '主任', '书记']):
                stats['关键人员数量'] += 1
        
        print("关键信息统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_document_content()
