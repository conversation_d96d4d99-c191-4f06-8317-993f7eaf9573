# AI Report 配置 v1.0

## 设计理念
基于苹果设计语言的企业画像报告模板，注重简洁、现代、专业的视觉呈现。

## 颜色方案
### 主色调系统
- **主色调**: #4a90a4 (蓝绿色)
- **辅助色1**: #5ba3b4 (浅蓝绿)
- **辅助色2**: #6b73a0 (蓝紫色)
- **辅助色3**: #7b83b0 (浅蓝紫)
- **辅助色4**: #8b93c0 (淡蓝紫)
- **辅助色5**: #8b5a9c (紫色)

### 功能色彩
- **成功色**: #5ba3b4
- **警告色**: #6b73a0
- **信息色**: #7b83b0
- **次要色**: #8b93c0

## 字体系统
```css
font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
```

## 布局规范
### 容器设置
- **最大宽度**: 1400px (适配14寸MacBook)
- **边距**: 20px (响应式)
- **圆角**: 20px (主容器), 16px (卡片), 12px (小元素)

### 间距系统
- **大间距**: 40px (章节间)
- **中间距**: 30px (子章节间)
- **小间距**: 20px (元素间)
- **微间距**: 16px (内容间)

## 组件规范

### 导航系统
#### 主导航
- **背景**: rgba(255, 255, 255, 0.8) + backdrop-filter: blur(20px)
- **激活状态**: 主色调背景 + 白色文字
- **悬停效果**: translateY(-1px)

#### 二级导航
- **显示条件**: 仅在"战客动态"章节显示
- **背景**: 主色调5%透明度
- **文字颜色**: 主色调

### 卡片系统
#### 基础卡片
```css
background: white;
border-radius: 16px;
padding: 24px;
box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
border: 1px solid rgba(0, 0, 0, 0.05);
```

#### 评级卡片
- **渐变背景**: 使用主色调系统的渐变组合
- **毛玻璃效果**: backdrop-filter: blur(10px)
- **星级评分**: 金色 #ffd700

#### 人员卡片
- **背景**: 多色渐变 (主色调 → 辅助色2 → 辅助色5)
- **装饰元素**: 右上角半透明圆形

### 按钮系统
#### 基础样式
```css
padding: 8px 16px;
border-radius: 20px;
font-size: 12px;
font-weight: 500;
transition: all 0.3s ease;
```

#### 颜色分类
- **主要按钮**: #4a90a4
- **成功按钮**: #5ba3b4
- **警告按钮**: #6b73a0
- **信息按钮**: #7b83b0
- **次要按钮**: #8b93c0

### 表格系统
#### 样式规范
- **头部背景**: #f8f9fa
- **边框**: #e9ecef (头部), #f2f2f7 (行间)
- **悬停效果**: #f8f9fa 背景
- **圆角容器**: 12px

### 信息网格
#### 布局
```css
display: grid;
grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
gap: 16px;
```

#### 信息项样式
- **背景**: #f8f9fa
- **圆角**: 12px
- **悬停效果**: translateY(-2px) + 阴影增强

## 动画效果

### 页面加载
```css
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### 交互反馈
- **悬停**: translateY(-2px) + 阴影变化
- **点击**: 涟漪效果 (0.6s)
- **过渡**: all 0.3s ease

## 响应式设计

### 断点设置
- **桌面**: > 1440px
- **笔记本**: 768px - 1440px
- **移动端**: < 768px

### 适配规则
#### 1440px以下
```css
.container {
    margin: 20px;
    max-width: calc(100% - 40px);
}
```

#### 768px以下
- 导航项字体: 12px
- 导航内边距: 6px 12px
- 标题字体: 24px
- 信息网格: 单列布局

## 内容结构

### 标准章节
1. **战客动态** (含7个子章节)
   - 基础信息
   - 价值度评级
   - 经营画像
   - 行业画像
   - 关键人
   - 主管单位
   - 重点项目

2. **政策信息**
3. **行业热点**
4. **招投标信息**
5. **商机类型**

### 特殊处理
- **空状态**: 使用图标 + 说明文字
- **评分显示**: 彩色徽章 + 数值
- **状态标签**: 不同颜色区分状态

## 技术实现

### CSS特性
- **毛玻璃效果**: backdrop-filter: blur(20px)
- **渐变背景**: linear-gradient(135deg, ...)
- **自定义滚动条**: webkit-scrollbar 样式
- **平滑滚动**: scroll-behavior: smooth

### JavaScript功能
- **章节切换**: showSection()
- **平滑滚动**: scrollToElement()
- **交互增强**: 悬停效果、点击反馈
- **初始化**: DOMContentLoaded 事件

## 文件命名规范
- **基础版本**: [企业名称]-企业画像报告.html
- **时间戳版本**: [企业名称]-企业画像报告_[时分].html
- **配置文件**: AI report 配置v[版本号].md

## 数据准确性规范

### 数据来源分类
#### ✅ 原文档直接数据
- **投资金额**: 必须来自原文明确数字
- **时间节点**: 必须来自原文明确表述
- **项目名称**: 必须与原文完全一致
- **服务内容**: 必须基于原文技术规格

#### ⚠️ 合理推测数据
- **效果目标**: 基于项目性质的行业标准推测
- **商机评分**: 基于项目规模和重要性评估
- **项目分类**: 显性/准显性/预判商机分类
- **必须标注**: 推测性质，避免误导

#### ❌ 禁止添加数据
- **具体时间段**: 如"2025.01-2026.12"（原文未明确时）
- **精确预算**: 如"约XX万元"（原文未提及时）
- **虚构联系方式**: 电话、邮箱等敏感信息
- **未验证的技术规格**: 带宽、容量等具体参数

### 质量检查流程

#### 第一步：完整性检查
```
□ 原文档是否完整提取？
□ 所有章节是否都已处理？
□ 表格行数是否与原文一致？
□ 文档末尾内容是否遗漏？
```

#### 第二步：数据准确性验证
```
□ 所有数字是否来自原文？
□ 时间信息是否准确标注？
□ "未明确"状态是否正确保留？
□ 推测信息是否合理标注？
```

#### 第三步：结构完整性检查
```
□ 商机类型数量是否完整？
□ 表格结构是否与原文匹配？
□ 关键信息是否有遗漏？
□ 跨页内容是否完整处理？
```

#### 第四步：最终验证
```
□ 逐项对比原文档
□ 检查容易遗漏的部分
□ 验证数据源标注
□ 确认无虚构信息
```

### 常见问题及避免方法

#### 🚨 容易遗漏的部分
- **文档末尾**: 观测商机、补充信息
- **表格最后几行**: 特别是商机类型表格
- **附录内容**: 政策文件、招投标信息
- **跨页内容**: 长表格的后续部分

#### 🛠️ 改进措施
1. **分段处理**: 将长文档分成小段逐一处理
2. **双重验证**: 处理完后再次完整检查
3. **标记进度**: 明确标记已处理和未处理部分
4. **交叉验证**: 转换后与原文逐项对比

#### 📋 检查清单模板
```
原文档信息统计：
- 商机总数: __ 个
- 表格总行数: __ 行
- 主要章节数: __ 个
- 关键数据点: __ 个

转换结果验证：
- 商机数量: __ 个 ✅/❌
- 表格行数: __ 行 ✅/❌
- 章节完整: ✅/❌
- 数据准确: ✅/❌
```

## 质量标准
- ✅ 无横向滚动 (14寸MacBook)
- ✅ 完整内容保留 (无遗漏)
- ✅ 数据准确性 (来源可追溯)
- ✅ 专业视觉设计
- ✅ 流畅交互体验
- ✅ 响应式适配
- ✅ 代码简洁清晰

## 强化质量控制流程 (v2.0)

### 🚨 强制性检查流程

#### 阶段一：信息提取完整性验证
**必须在开始转换前完成**
1. **原文档全文解析**：
   ```
   □ 逐行读取原文档内容
   □ 建立信息提取清单
   □ 统计各类信息数量
   □ 标记重要数据位置
   ```

2. **关键信息统计**：
   ```
   □ 商机总数：___ 个
   □ 政策文件数：___ 个
   □ 荣誉奖项数：___ 个
   □ DICT项目数：___ 个
   □ 关键人员数：___ 个
   □ 行业热点数：___ 个
   ```

#### 阶段二：逐章节验证机制
**每完成一个章节立即执行**

1. **基础信息验证**：
   ```
   □ 企业名称：完全一致
   □ 成立日期：格式和内容准确
   □ 注册资金：数字和单位准确
   □ 经营地址：完整准确
   □ 隶属关系：表述准确
   ```

2. **价值度评级验证**：
   ```
   □ 评级星级：与原文一致
   □ 评级依据：完整引用原文
   □ 价值点：准确概括
   □ 具体数据：如营收数字必须准确
   ```

3. **行业画像验证**：
   ```
   □ 行业地位：所有要点都包含
   □ 荣誉奖项：逐项核对，确保无遗漏
   □ 专利数量：数字准确
   □ 市场地位：排名和份额准确
   ```

#### 阶段三：最终完整性检查
**转换完成后强制执行**

1. **数量核对**：
   ```
   □ 商机数量：原文 ___ 个 = HTML ___ 个 ✅
   □ 政策数量：原文 ___ 个 = HTML ___ 个 ✅
   □ 荣誉数量：原文 ___ 个 = HTML ___ 个 ✅
   □ 项目数量：原文 ___ 个 = HTML ___ 个 ✅
   ```

2. **内容完整性**：
   ```
   □ 每个荣誉奖项都已包含
   □ 每个政策文件都已包含
   □ 每个DICT项目都已包含
   □ 每个商机都已包含
   □ 所有关键数据都已包含
   ```

### 🔍 错误预防机制

#### 信息遗漏预防
1. **使用检查清单**：每个信息类别建立独立清单
2. **分段验证**：每50行内容进行一次核对
3. **交叉引用**：同一信息在不同位置的一致性检查

#### 数据错误预防
1. **原文档唯一参考**：不依赖记忆或推测
2. **数字格式统一**：金额、时间、数量的表述格式
3. **术语准确性**：专业术语和企业名称的准确性

#### 🆕 信息一致性检查机制
**问题**：同一信息在不同章节出现时可能不一致
**解决方案**：
1. **重复信息标识**：
   ```
   □ 标记所有在多个章节出现的信息
   □ 建立重复信息对照表
   □ 确保每次引用都完整一致
   ```

2. **括号内容特别检查**：
   ```
   □ 所有括号内的补充信息都必须保留
   □ 特别注意：（亚洲最大）、（全国第一）等重要标识
   □ 检查：（营收超900亿元）、（世界500强）等关键数据
   ```

3. **完整性对比验证**：
   ```
   □ 同一句话在不同位置必须完全一致
   □ 不允许任意截断或简化
   □ 重要修饰词不能丢失
   ```

#### 🚨 高风险信息类型
**以下信息类型容易出现截断，需特别注意**：
1. **技术规格**：产能数据 + 技术特色描述
2. **市场地位**：排名数据 + 地位描述
3. **企业规模**：营收数据 + 规模描述
4. **技术优势**：专利数量 + 技术突破描述

#### 强制一致性检查清单
```
□ 企业规模描述在各章节是否完全一致？
□ 技术能力描述是否保持完整？
□ 市场地位表述是否一致？
□ 所有括号内容是否都保留？
□ 重要修饰词是否都包含？
```

### 📋 详细检查清单模板

#### 战客动态章节
```
基础信息 (8项):
□ 企业名称 □ 成立日期 □ 企业属性 □ 注册资金
□ 实缴资金 □ 经营地址 □ 隶属关系 □ 分支机构

价值度评级 (3项):
□ 品牌价值评级 □ 收益价值评级 □ 合作紧密度评级

经营画像 (3项):
□ 主营业务范围 □ 产业链定位 □ 收入规模

行业画像:
□ 行业地位 (所有要点) □ 荣誉奖项 (逐项核对) □ 专利情况

关键人:
□ 所有人员姓名 □ 职务准确 □ 职责描述 □ 其他信息

重点项目:
□ 标杆项目 □ 近期项目 □ DICT项目 (逐项核对)
```

#### 其他章节
```
政策信息:
□ 按地域正确分类 □ 文件名称完整 □ 发布单位准确

行业热点:
□ 热点方向 □ 案例描述 □ 效果说明 □ 潜在商机

商机类型:
□ 商机数量准确 □ 评分正确 □ 分类准确 □ 描述完整
```

### ⚠️ 质量红线
**以下情况必须停止并重新检查**：
1. 发现任何信息遗漏
2. 发现数据不一致
3. 无法在原文档中找到信息来源
4. 数量统计不匹配

### 🎯 质量目标
- **完整性**: 100% (无任何遗漏)
- **准确性**: 100% (关键数据零错误)
- **一致性**: 100% (同一信息保持一致)
- **可追溯性**: 100% (每个信息点都能追溯到原文)

## 🚨 强制性原文档读取流程 (v3.0)

### 📖 第一步：强制原文档完整读取
**在开始任何HTML生成工作前，必须执行以下步骤**：

#### 1.1 使用python-docx完整提取文档内容
```python
# 必须执行的代码模板
import docx
doc = docx.Document("原文档路径")

# 提取所有段落
paragraphs = []
for i, para in enumerate(doc.paragraphs):
    text = para.text.strip()
    if text:
        paragraphs.append({'index': i + 1, 'text': text})

# 提取所有表格
tables = []
for i, table in enumerate(doc.tables):
    table_data = []
    for row in table.rows:
        row_data = [cell.text.strip() for cell in row.cells]
        table_data.append(row_data)
    tables.append(table_data)

print(f"总段落数: {len(paragraphs)}")
print(f"总表格数: {len(tables)}")
```

#### 1.2 基础信息强制验证
**必须逐项查找并确认以下信息**：
```
□ 企业名称：在第___行找到，内容为：___
□ 成立日期：在第___行找到，内容为：___
□ 企业属性：在第___行找到，内容为：___
□ 注册资金：在第___行找到，内容为：___
□ 实缴资金：在第___行找到，内容为：___
□ 经营地址：在第___行找到，内容为：___
□ 隶属关系：在第___行找到，内容为：___
□ 分支机构：在第___行找到，内容为：___
```

#### 1.3 价值度评级强制验证
**必须逐项查找并确认星级评级**：
```
□ 品牌价值评级：★数量___，在第___行
□ 收益价值评级：★数量___，在第___行
□ 合作紧密度评级：★数量___，在第___行
□ 评级依据：完整复制原文，不允许改写
□ 价值点：完整复制原文，不允许改写
```

#### 1.4 商机表格强制验证
**必须完整提取商机表格**：
```
□ 商机表格位置：第___个表格
□ 表格行数：___行（包含表头）
□ 表格列数：___列
□ 逐行验证：每行内容必须与原文完全一致
□ 评分格式：必须保持原文格式（如0.85而非"高"）
```

### 🔒 第二步：禁止推测和虚构信息
**绝对禁止的行为**：
1. ❌ **基于常识推测**：不允许根据企业名称推测行业信息
2. ❌ **参考其他企业**：不允许参考同行业其他企业信息
3. ❌ **美化或补充**：不允许为了"完整性"而添加未明确的信息
4. ❌ **格式转换**：不允许将原文的具体数据转换为模糊表述

**必须遵循的原则**：
1. ✅ **原文唯一来源**：所有信息必须来自原文档
2. ✅ **逐字对照**：关键信息必须与原文逐字对照
3. ✅ **未知即未知**：原文未明确的信息标注为"未知"
4. ✅ **完整保留**：括号内容、修饰词等必须完整保留

### 📋 第三步：逐章节对照检查
**每完成一个章节后立即执行**：

#### 3.1 基础信息对照
```
原文第4行：企业名称：山东新和成控股有限公司（简称：山东新和成）
HTML对应：✅ 完全一致 / ❌ 不一致，差异：___

原文第5行：成立日期：2017年2月10日
HTML对应：✅ 完全一致 / ❌ 不一致，差异：___

原文第7行：注册资金：20000万元
HTML对应：✅ 完全一致 / ❌ 不一致，差异：___
```

#### 3.2 评级信息对照
```
原文第14行：评级：★★★☆☆
HTML对应：✅ 星级一致 / ❌ 星级错误

原文第16-17行：依据内容
HTML对应：✅ 完整一致 / ❌ 有删减或改写
```

#### 3.3 商机表格对照
```
原文表格行数：7行
HTML表格行数：___行 ✅ 一致 / ❌ 不一致

原文第2行第5列：0.85
HTML对应：✅ 格式一致 / ❌ 格式错误（如改为"高"）
```

### 🛡️ 第四步：最终完整性验证
**HTML生成完成后强制执行**：

#### 4.1 数量统计验证
```
□ 基础信息项目：原文8项 = HTML___项
□ 价值评级项目：原文3项 = HTML___项
□ 荣誉奖项数量：原文___项 = HTML___项
□ 商机表格行数：原文7行 = HTML___行
□ 政策文件数量：原文___个 = HTML___个
```

#### 4.2 关键数据验证
```
□ 成立日期：2017年2月10日 ✅
□ 注册资金：20000万元 ✅
□ 企业属性：有限责任公司（自然人投资或控股的法人独资） ✅
□ 品牌价值评级：★★★☆☆ ✅
□ 商机评分格式：0.85, 0.78, 0.72... ✅
```

### 🚨 错误案例警示
**以下是真实发生的错误，必须避免**：

#### 错误案例1：基础信息错误
```
❌ 错误：成立日期：1999年（基于推测）
✅ 正确：成立日期：2017年2月10日（原文第5行）

❌ 错误：注册资金：50亿元人民币（基于推测）
✅ 正确：注册资金：20000万元（原文第7行）

❌ 错误：企业属性：民营企业（基于推测）
✅ 正确：企业属性：有限责任公司（自然人投资或控股的法人独资）（原文第6行）
```

#### 错误案例2：评级信息错误
```
❌ 错误：品牌价值评级：★★★★☆（基于推测）
✅ 正确：品牌价值评级：★★★☆☆（原文第14行）

❌ 错误：评级依据：全球维生素A、维生素E产能第一（虚构）
✅ 正确：评级依据：多次获得国家级及区域级奖项，如"全国工业和信息化系统先进集体"（原文第16行）
```

#### 错误案例3：商机表格错误
```
❌ 错误：落地评分显示为"高"、"中"、"低"
✅ 正确：落地评分显示为"0.85"、"0.78"、"0.72"等数值

❌ 错误：商机场景为"智能工厂建设"（虚构）
✅ 正确：商机场景为"能耗在线监测+碳管理平台"（原文表格第2行第1列）
```

### 📝 强制执行检查清单
**每次处理文档时必须填写**：
```
□ 已使用python-docx完整读取原文档
□ 已逐行查找并确认所有基础信息
□ 已逐项确认所有评级信息和星级
□ 已完整提取并验证商机表格
□ 已逐章节对照检查原文与HTML
□ 已执行最终完整性验证
□ 确认无任何推测或虚构信息
□ 确认所有数据可追溯到原文具体位置
```

### 🎯 质量承诺
**处理每个文档时必须做出以下承诺**：
1. 我已完整读取原文档内容
2. 我未添加任何推测或虚构信息
3. 我已逐项验证所有关键数据
4. 我确认HTML内容与原文完全一致
5. 我可以为每个信息点提供原文位置

## 🛠️ Python脚本辅助工具使用规范 (v1.0)

### 📋 脚本使用场景
**必须使用Python脚本的情况**：
1. 文档内容复杂，段落数量超过100个
2. 包含多个表格需要精确提取
3. 关键人信息格式复杂，需要解析分析
4. 首次处理某类型文档，需要摸清数据结构

### 🔧 标准脚本模板

#### 基础文档提取脚本
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档内容提取脚本
严格按照AI report配置v1.md强制性原文档读取流程
"""

import docx
import sys
import os

def extract_document_content():
    doc_path = "文档路径"

    if not os.path.exists(doc_path):
        print(f"文件不存在: {doc_path}")
        return

    try:
        doc = docx.Document(doc_path)

        print("=== 文档内容提取 ===")

        # 1. 提取所有段落
        paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                paragraphs.append({
                    'index': i + 1,
                    'text': text
                })

        print(f"总段落数: {len(paragraphs)}")

        # 2. 输出完整段落内容
        print("=== 完整段落内容 ===")
        for para in paragraphs:
            print(f"{para['index']:3d}: {para['text']}")

        # 3. 提取表格内容
        print("=== 表格内容 ===")
        for i, table in enumerate(doc.tables):
            print(f"\n表格 {i+1} ({len(table.rows)}行 x {len(table.columns) if table.rows else 0}列):")
            for j, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_data.append(cell_text)
                print(f"  行{j+1}: {' | '.join(row_data)}")

        # 4. 基础信息强制验证
        print("=== 基础信息强制验证 ===")
        basic_keywords = ['企业名称', '成立日期', '注册资金', '经营地址', '企业属性', '实缴资金', '隶属关系', '分支机构']

        for keyword in basic_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")

        # 5. 价值度评级强制验证
        print("=== 价值度评级强制验证 ===")
        rating_keywords = ['价值评级', '品牌价值', '收益价值', '合作紧密度', '★', '星']

        for keyword in rating_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")

        # 6. 商机表格强制验证
        print("=== 商机表格强制验证 ===")
        if len(doc.tables) > 0:
            for i, table in enumerate(doc.tables):
                print(f"\n分析表格{i+1}:")
                if len(table.rows) > 0:
                    headers = [cell.text.strip() for cell in table.rows[0].cells]
                    print(f"  表头: {' | '.join(headers)}")

                    # 判断是否为商机表格
                    header_text = ' '.join(headers)
                    if any(keyword in header_text for keyword in ['商机', '场景', '方案', '痛点', '竞争', '评分']):
                        print(f"  -> 识别为商机表格")
                        print(f"  -> 总行数: {len(table.rows)}行（包含表头）")
                        print(f"  -> 数据行数: {len(table.rows)-1}行")

                        # 输出所有数据行
                        for j, row in enumerate(table.rows[1:], 1):
                            row_data = [cell.text.strip() for cell in row.cells]
                            print(f"    数据行{j}: {' | '.join(row_data)}")
        else:
            print("  未找到任何表格")

    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_document_content()
```

#### 关键人信息专用脚本
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键人信息提取脚本
专门用于解析复杂的人员信息格式
"""

import docx
import sys
import os

def extract_key_persons():
    doc_path = "文档路径"

    if not os.path.exists(doc_path):
        print(f"文件不存在: {doc_path}")
        return

    try:
        doc = docx.Document(doc_path)

        print("=== 关键人信息提取 ===")

        # 提取所有段落
        paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                paragraphs.append({
                    'index': i + 1,
                    'text': text
                })

        # 查找关键人相关信息
        print("=== 关键人相关段落 ===")
        key_person_paragraphs = []

        # 扩展关键词列表，适应不同行业
        keywords = ['院长', '副院长', '书记', '主任', '关键人', '负责人', '对接人', '联系方式', '分管', '主持',
                   '董事长', '总经理', '副总', '总裁', '经理', '主管', '负责人', '联系人']

        for para in paragraphs:
            text = para['text']
            if any(keyword in text for keyword in keywords):
                key_person_paragraphs.append(para)
                print(f"{para['index']:3d}: {text}")

        print("=== 具体人员信息分析 ===")

        # 分析人员信息
        persons = []

        for para in key_person_paragraphs:
            text = para['text']

            # 查找包含姓名和职务的段落（支持多种格式）
            if '：' in text and any(title in text for title in keywords):
                # 分析格式：姓名：职务，分工描述
                parts = text.split('：')
                if len(parts) >= 2:
                    name = parts[0].strip()
                    rest = '：'.join(parts[1:]).strip()

                    # 进一步分析职务和描述
                    if '，' in rest:
                        title_desc = rest.split('，')
                        title = title_desc[0].strip()
                        desc = '，'.join(title_desc[1:]).strip()
                    else:
                        title = rest
                        desc = ""

                    persons.append({
                        'name': name,
                        'title': title,
                        'desc': desc,
                        'source_line': para['index']
                    })

                    print(f"姓名: {name}")
                    print(f"职务: {title}")
                    print(f"描述: {desc}")
                    print(f"来源行: {para['index']}")
                    print("---")

        print(f"=== 提取到的人员信息总数: {len(persons)} ===")

        for i, person in enumerate(persons, 1):
            print(f"{i}. {person['name']} - {person['title']}")
            if person['desc']:
                print(f"   {person['desc']}")

        # 查找其他相关信息
        print("=== 其他相关信息 ===")

        for para in paragraphs:
            text = para['text']
            if any(keyword in text for keyword in ['实控人', '社会角色', '母公司', '对接人', '联系方式']) and '：' in text:
                print(f"{para['index']:3d}: {text}")

    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_key_persons()
```

### ⚠️ 脚本使用注意事项

#### 1. 文件路径处理
```python
# ✅ 正确：使用相对路径
doc_path = "0715  战客动态2家/企业名称-战客动态.docx"

# ❌ 错误：使用绝对路径（不便于移植）
doc_path = "/Users/<USER>/Documents/企业名称-战客动态.docx"
```

#### 2. 异常处理
```python
# ✅ 必须包含完整的异常处理
try:
    doc = docx.Document(doc_path)
    # 处理逻辑
except Exception as e:
    print(f"处理失败: {e}")
    import traceback
    traceback.print_exc()
```

#### 3. 数据结构标准化
```python
# ✅ 标准化的段落数据结构
paragraphs.append({
    'index': i + 1,  # 1基索引，便于定位
    'text': text     # 清理后的文本
})

# ✅ 标准化的人员数据结构
persons.append({
    'name': name,
    'title': title,
    'desc': desc,
    'source_line': para['index']  # 可追溯性
})
```

#### 4. 关键词扩展性
```python
# ✅ 根据不同行业扩展关键词
# 医疗行业
medical_keywords = ['院长', '副院长', '书记', '主任', '科长']

# 企业行业
business_keywords = ['董事长', '总经理', '副总', '总裁', '经理']

# 政府机构
government_keywords = ['局长', '副局长', '处长', '科长', '主任']
```

#### 5. 输出格式规范
```python
# ✅ 统一的输出格式
print("=== 章节标题 ===")
print(f"{para['index']:3d}: {para['text']}")  # 3位数字对齐
print(f"  -> 分析结果")  # 缩进表示层级
```

### 🚨 常见错误及避免方法

#### 错误1：遗漏关键人信息
```python
# ❌ 错误：关键词不全
keywords = ['院长', '副院长']

# ✅ 正确：扩展关键词列表
keywords = ['院长', '副院长', '书记', '主任', '纪委书记', '党委书记']
```

#### 错误2：格式解析不准确
```python
# ❌ 错误：简单分割
name, title = text.split('：')

# ✅ 正确：考虑多种格式
if '：' in text:
    parts = text.split('：')
    if len(parts) >= 2:
        name = parts[0].strip()
        rest = '：'.join(parts[1:]).strip()
```

#### 错误3：缺少可追溯性
```python
# ❌ 错误：无法追溯信息来源
persons.append({'name': name, 'title': title})

# ✅ 正确：记录来源行号
persons.append({
    'name': name,
    'title': title,
    'source_line': para['index']
})
```

### 📝 脚本使用检查清单
```
□ 文件路径正确且存在
□ 包含完整异常处理
□ 使用标准化数据结构
□ 关键词列表完整
□ 输出格式规范统一
□ 记录信息来源行号
□ 验证提取结果完整性
□ 测试脚本运行无错误
```

## 🎯 稳定复现报告的最佳方案 (v2.0)

### 📊 方案对比分析

| 方案 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **纯MD文档** | 流程清晰、易维护 | 人工验证易出错 | 简单文档、熟悉格式 |
| **纯Python脚本** | 自动化程度高、准确性强 | 开发成本高、不够灵活 | 复杂文档、批量处理 |
| **MD+Python混合** | 兼具灵活性和准确性 | 需要两套工具 | **推荐方案** |

### 🏆 推荐方案：MD文档 + Python脚本混合模式

#### 核心理念
```
MD文档定义标准流程 + Python脚本确保数据准确性 = 稳定可复现的高质量报告
```

#### 具体实施方案

##### 阶段一：Python脚本数据提取（强制执行）
```bash
# 1. 运行基础文档提取脚本
python3 extract_document_content.py

# 2. 运行关键人信息提取脚本（如需要）
python3 extract_key_persons.py

# 3. 验证提取结果完整性
```

**输出结果**：
- 完整段落列表（带行号）
- 表格内容（逐行逐列）
- 基础信息验证结果
- 价值评级验证结果
- 商机表格验证结果
- 关键人信息解析结果

##### 阶段二：基于脚本结果生成HTML（MD文档指导）
```markdown
严格按照AI report配置v1.md的HTML生成流程：
1. 使用脚本提取的准确数据
2. 遵循标准化呈现格式
3. 执行逐章节质量验证
4. 确保可追溯性
```

#### 工作流程图
```
原始Word文档
    ↓
Python脚本自动提取
    ↓
数据验证与清洗
    ↓
MD文档流程指导
    ↓
HTML报告生成
    ↓
质量检查验证
    ↓
最终报告输出
```

### 🔧 技术实现方案

#### 1. 标准化脚本库
```
extract_tools/
├── base_extractor.py          # 基础提取功能
├── key_person_extractor.py    # 关键人信息提取
├── table_extractor.py         # 表格数据提取
├── rating_extractor.py        # 评级信息提取
└── validator.py               # 数据验证工具
```

#### 2. 配置文件驱动
```yaml
# document_config.yaml
document_type: "enterprise_profile"
required_sections:
  - basic_info
  - value_rating
  - business_profile
  - industry_profile
  - key_persons
  - main_authority
  - key_projects

extraction_rules:
  basic_info:
    keywords: ["企业名称", "成立日期", "注册资金"]
    required: true
  key_persons:
    keywords: ["董事长", "总经理", "院长", "副院长"]
    format: "name:title,description"
```

#### 3. 自动化验证机制
```python
def validate_extraction_results(extracted_data, original_doc):
    """
    验证提取结果的完整性和准确性
    """
    validation_results = {
        'basic_info_complete': check_basic_info(extracted_data),
        'rating_stars_correct': check_rating_format(extracted_data),
        'table_data_accurate': check_table_consistency(extracted_data, original_doc),
        'key_persons_complete': check_key_persons(extracted_data),
        'traceable_sources': check_source_lines(extracted_data)
    }

    return validation_results
```

### 📋 标准化操作流程

#### Step 1: 文档预处理
```bash
# 检查文档格式和完整性
python3 validate_document.py input_document.docx
```

#### Step 2: 数据提取
```bash
# 运行标准提取流程
python3 extract_all_data.py input_document.docx --output extraction_results.json
```

#### Step 3: 数据验证
```bash
# 验证提取结果
python3 validate_extraction.py extraction_results.json
```

#### Step 4: HTML生成
```bash
# 基于验证后的数据生成HTML
python3 generate_html_report.py extraction_results.json --template enterprise_profile
```

#### Step 5: 质量检查
```bash
# 最终质量检查
python3 quality_check.py output_report.html extraction_results.json
```

### 🎯 方案优势

#### 1. 数据准确性保障
- **Python脚本**：确保100%数据提取准确性
- **自动验证**：防止人工遗漏和错误
- **可追溯性**：每个数据点都有源文档行号

#### 2. 流程标准化
- **MD文档**：定义标准流程和质量要求
- **模板化**：统一的HTML输出格式
- **可复现性**：相同输入必然产生相同输出

#### 3. 效率提升
- **自动化**：减少90%的人工验证工作
- **批量处理**：支持多文档并行处理
- **错误预防**：提前发现和修正问题

#### 4. 可维护性
- **模块化设计**：各功能模块独立可测试
- **配置驱动**：适应不同文档类型和要求
- **版本控制**：脚本和配置文件统一管理

### 🚀 实施建议

#### 短期目标（1-2周）
1. 完善现有提取脚本，增加更多验证功能
2. 创建标准化的脚本模板库
3. 建立质量检查清单

#### 中期目标（1个月）
1. 开发配置文件驱动的提取系统
2. 实现自动化的HTML生成流程
3. 建立完整的测试用例库

#### 长期目标（2-3个月）
1. 构建可视化的质量监控面板
2. 实现智能化的文档类型识别
3. 开发批量处理和报告对比功能

### 📊 成功指标

#### 质量指标
- **数据准确率**：≥99.5%
- **信息完整率**：≥99%
- **格式一致性**：100%

#### 效率指标
- **处理时间**：单文档≤10分钟
- **人工验证时间**：减少90%
- **错误修正时间**：减少80%

#### 可维护性指标
- **脚本复用率**：≥80%
- **配置文件覆盖率**：100%
- **文档类型支持**：≥5种

这个混合方案既保证了数据的准确性，又保持了流程的灵活性，是实现稳定复现高质量报告的最佳选择！
