# AI Report 企业画像报告生成配置 v2.0

## 🎯 核心理念

**MD文档定义标准流程 + Python脚本确保数据准确性 = 稳定可复现的高质量报告**

## 📊 工作流程

```
原始Word文档 → Python脚本自动提取 → 数据验证与清洗 → MD文档流程指导 → HTML报告生成 → 质量检查验证 → 最终报告输出
```

## 🔧 标准化操作流程

### Step 1: 文档预处理
```bash
# 检查文档是否存在且格式正确
python3 "AI Report 脚本.py" --validate "文档路径.docx"
```

### Step 2: 数据提取（强制执行）
```bash
# 运行完整数据提取流程
python3 "AI Report 脚本.py" --extract "文档路径.docx"
```

### Step 3: HTML报告生成
基于脚本提取的准确数据，严格按照以下标准生成HTML报告：

#### 必须包含的五大章节：
1. **一、战客动态**（7个子章节）
2. **二、政策信息**
3. **三、行业热点**
4. **四、招投标信息**
5. **五、商机类型**

#### 文件命名规则：
```
原文件名 + "_Aug.html"
例：潍坊市中医院-战客动态.docx → 潍坊市中医院-战客动态_Aug.html
```

## 📋 数据提取验证清单

### ✅ 基础信息验证（必须8项）
- [ ] 企业名称
- [ ] 成立日期
- [ ] 注册资金
- [ ] 经营地址
- [ ] 企业属性
- [ ] 实缴资金
- [ ] 隶属关系
- [ ] 分支机构

### ✅ 价值度评级验证（必须3项）
- [ ] 品牌价值评级（★★★☆☆格式）
- [ ] 收益价值评级（★★★☆☆格式）
- [ ] 合作紧密度评级（★★★☆☆格式）

### ✅ 关键人信息验证
- [ ] 所有关键人员姓名、职务、分工
- [ ] 实控人社会角色
- [ ] 母公司画像
- [ ] 对接人信息

### ✅ 商机表格验证
- [ ] 商机数量准确
- [ ] 评分格式保持原文（如0.85、0.78等）
- [ ] 场景描述完整

### ✅ 政策信息验证
- [ ] 政策文件名称、文号、发布单位
- [ ] 发布日期、政策类型
- [ ] 主要内容摘要

## 🎨 HTML呈现标准

### 导航系统
```html
<!-- 一级导航：5个主要章节 -->
<div class="nav-primary">
    <button class="nav-item active" onclick="showSection('section1', this)">一、战客动态</button>
    <button class="nav-item" onclick="showSection('section2', this)">二、政策信息</button>
    <!-- ... -->
</div>

<!-- 二级导航：仅在"战客动态"章节显示 -->
<div class="nav-secondary" id="nav-secondary">
    <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
    <!-- ... -->
</div>
```

### 关键人信息呈现
```html
<!-- 使用网格布局，一行多个卡片 -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
    <div class="person-card">
        <div class="person-name">姓名</div>
        <div class="person-title">职务</div>
        <div class="person-desc">分工描述。联系方式：未公开</div>
    </div>
</div>

<!-- 补充信息使用info-grid -->
<div class="card" style="margin-top: 20px;">
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">实控人社会角色</div>
            <div class="info-value">具体信息</div>
        </div>
    </div>
</div>
```

### 价值度评级呈现
```html
<!-- 横向布局，星级在右侧 -->
<div class="rating-card">
    <div class="content">
        <div class="rating-title">
            品牌价值评级
            <span class="rating-stars">★★★☆☆</span>
        </div>
        <div class="rating-details">
            <strong>依据：</strong>具体依据内容<br>
            <strong>价值点：</strong>具体价值点描述
        </div>
    </div>
</div>
```

### 商机类型呈现
```html
<!-- 保持原文数值格式 -->
<td><span class="btn btn-success">0.90</span></td>
<td><span class="btn btn-warning">0.78</span></td>
```

## 🚨 质量控制要点

### 数据准确性
1. **100%基于原文档**：不得添加任何推测或虚构信息
2. **保持原始格式**：数值、日期、评分等严格按原文
3. **完整性检查**：确保所有章节内容完整
4. **可追溯性**：每个信息点都能追溯到原文具体位置

### 呈现一致性
1. **参照成功案例**：严格按照潍坊烟草等成功案例的呈现形式
2. **样式统一**：使用相同的CSS样式和布局结构
3. **交互功能**：确保导航、切换等JavaScript功能正常

### 命名规范
1. **文件命名**：原文件名 + "_Aug.html"
2. **保存位置**：与原文档相同目录
3. **编码格式**：UTF-8

## 📊 成功指标

### 质量指标
- **数据准确率**：≥99.5%
- **信息完整率**：≥99%
- **格式一致性**：100%

### 效率指标
- **处理时间**：单文档≤10分钟
- **人工验证时间**：减少90%
- **错误修正时间**：减少80%

## 🎯 质量承诺

**处理每个文档时必须做出以下承诺**：
1. ✅ 我已使用Python脚本完整提取原文档内容
2. ✅ 我已验证所有基础信息、价值评级、关键人信息的完整性
3. ✅ 我未添加任何推测或虚构信息
4. ✅ 我已确认HTML内容与原文完全一致
5. ✅ 我可以为每个信息点提供原文位置
6. ✅ 我已按照标准呈现格式生成HTML报告
7. ✅ 我已验证所有交互功能正常工作

## 🔄 持续改进

### 版本更新记录
- **v2.0**：引入Python脚本辅助，建立混合模式工作流程
- **v1.0**：基础MD文档配置，手工验证模式

### 反馈机制
发现问题时，及时更新配置文档和脚本工具，确保持续改进和优化。
