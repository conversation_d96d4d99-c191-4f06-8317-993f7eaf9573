<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：弘润石化（潍坊）有限责任公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 内容区域 */
        .content {
            padding: 0 30px 30px;
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 30px 0 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4a90a4;
            letter-spacing: -0.3px;
        }

        .subsection {
            margin-bottom: 30px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .subsection-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #4a90a4;
            border-radius: 2px;
            margin-right: 12px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 6px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            color: #1d1d1f;
            font-weight: 500;
            font-size: 14px;
        }

        /* 评级样式 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #5ba3b4 50%, #6cb6c4 100%);
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .rating-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .rating-card .content {
            position: relative;
            z-index: 1;
        }

        .rating-card:nth-child(2) {
            background: linear-gradient(135deg, #5ba3b4 0%, #6cb6c4 50%, #7dc9d4 100%);
        }

        .rating-card:nth-child(3) {
            background: linear-gradient(135deg, #6b73a0 0%, #7b83b0 50%, #8b93c0 100%);
        }

        .rating-card:nth-child(4) {
            background: linear-gradient(135deg, #8b5a9c 0%, #9b6aac 50%, #ab7abc 100%);
        }

        .rating-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rating-stars {
            color: #ffd700;
            font-size: 18px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .rating-details {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 16px 0;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #4a90a4;
            color: white;
        }

        .btn-primary:hover {
            background: #3a7a94;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #5ba3b4;
            color: white;
        }

        .btn-warning {
            background: #6b73a0;
            color: white;
        }

        .btn-info {
            background: #7b83b0;
            color: white;
        }

        .btn-secondary {
            background: #8b93c0;
            color: white;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin: 20px 0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #1d1d1f;
            font-size: 13px;
            border-bottom: 1px solid #e9ecef;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 13px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .card-title::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #4a90a4;
            border-radius: 50%;
            margin-right: 10px;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            border-radius: 16px;
            padding: 20px;
            margin: 12px 0;
            position: relative;
            overflow: hidden;
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-role {
            font-size: 13px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 评分样式 */
        .score-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .score-high {
            background: linear-gradient(135deg, #5ba3b4, #6cb6c4);
        }

        .score-medium {
            background: linear-gradient(135deg, #6b73a0, #7b83b0);
        }

        .score-low {
            background: linear-gradient(135deg, #8b5a9c, #9b6aac);
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-primary {
                flex-wrap: wrap;
                padding: 12px;
            }
            
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 0 20px 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a94;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-container">
            <div class="nav-primary">
                <a href="#section1" class="nav-item active" onclick="showSection('section1', this)">战客动态</a>
                <a href="#section2" class="nav-item" onclick="showSection('section2', this)">政策信息</a>
                <a href="#section3" class="nav-item" onclick="showSection('section3', this)">行业热点</a>
                <a href="#section4" class="nav-item" onclick="showSection('section4', this)">招投标信息</a>
                <a href="#section5" class="nav-item" onclick="showSection('section5', this)">商机类型</a>
            </div>
            <div class="nav-secondary" id="nav-secondary">
                <a href="#sub1-1" class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</a>
                <a href="#sub1-2" class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</a>
                <a href="#sub1-3" class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</a>
                <a href="#sub1-4" class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</a>
                <a href="#sub1-5" class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</a>
                <a href="#sub1-6" class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</a>
                <a href="#sub1-7" class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</a>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>企业画像报告</h1>
            <div class="subtitle">弘润石化（潍坊）有限责任公司</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 战客动态 -->
            <div id="section1" class="section">
                <div class="section-title">一、战客动态</div>
                
                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">企业名称（简称）</div>
                            <div class="info-value">弘润石化（潍坊）有限责任公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">成立日期</div>
                            <div class="info-value">1997年01月23日</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">企业属性</div>
                            <div class="info-value">有限责任公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">注册资金</div>
                            <div class="info-value">79,358万元</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实缴资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">经营地址</div>
                            <div class="info-value">潍坊高新技术产业开发区福寿东街中段</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">隶属关系</div>
                            <div class="info-value">无明确隶属关系，由董鹏实际控制</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要分支机构</div>
                            <div class="info-value">无明确提及</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>2024年山东民企百强第6位，潍坊第一大民企（营收超900亿元），化工产业"链主企业"，带动潍坊工业营收占比33.3%。<br>
                                <strong>价值点：</strong>行业地位高，市场影响力强，品牌知名度高。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>现有合作业务年贡献值：</strong>未知<br>
                                <strong>潜在业务合作规模预测：</strong>未知<br>
                                <strong>依据：</strong>作为大型石化企业，具备较强的盈利能力与市场拓展潜力。<br>
                                <strong>价值点：</strong>营收规模庞大，具备良好的合作基础和扩展空间。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>合作年限及续约情况：</strong>未知<br>
                                <strong>履约能力评估（历史项目交付质量）：</strong>未知<br>
                                <strong>依据：</strong>未提供具体合作记录，但作为行业龙头，具备较高的履约能力和合作意愿。<br>
                                <strong>价值点：</strong>具备较强的合作能力和履约保障。
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">柴油汽油、沥青、石油焦、丙烯丙烷、二甲苯高端聚丙烯新材料、III类润滑油、食品级白油等</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">
                                • 炼化综合能力800万吨/年<br>
                                • 储油能力1270万方（全国最大保税库）<br>
                                • 高端聚丙烯产能45万吨/年（亚洲最大智慧化无人车间）
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">2024年总额1395.28亿元（位列中国民企500强第61位）</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群</span>
                        <span class="btn btn-success">规上企业</span>
                        <span class="btn btn-success">专精特新企业</span>
                        <span class="btn btn-success">省级单项冠军</span>
                    </div>
                </div>

                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">2024年山东民企百强第6位，潍坊第一大民企（营收超900亿元）</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">化工产业"链主企业"，带动潍坊工业营收占比33.3%</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">炼化综合能力800万吨/年，储油能力1270万方（全国最大保税库）</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #7b83b0;">高端聚丙烯产能45万吨/年（亚洲最大智慧化无人车间）</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #8b93c0;">拥有261项专利，突破III类润滑油、食品级白油等"卡脖子"技术</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #8b5a9c;">国内首条聚酰亚胺全产业链生产线，市场占有率：间二甲苯全国第一</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">2024年山东民营企业200强（第6位）</span>
                            <span class="btn btn-success">国家绿色工厂</span>
                            <span class="btn btn-warning">潍坊市"40年·40人"杰出人物</span>
                            <span class="btn btn-info">全国劳动模范拟表彰对象</span>
                            <span class="btn btn-secondary">全国守合同重信用企业</span>
                            <span class="btn btn-primary">中国石油化工百强企业</span>
                            <span class="btn btn-success">山东省高新技术企业</span>
                            <span class="btn btn-info">山东省绿色低碳高质量发展先行区试点企业</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">资质认证</div>
                            <div class="info-value">
                                <span class="btn btn-info">规上企业</span>
                                <span class="btn btn-info">专精特新企业</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专利商标统计情况</div>
                            <div class="info-value">
                                专利总数：<strong>261项</strong>（截至2025年6月）<br>
                                核心技术领域：炼化工艺优化、新材料合成、环保技术
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div class="person-card">
                            <div class="person-name">董鹏</div>
                            <div class="person-role">董事长、法定代表人</div>
                            <div class="person-desc">主导集团整体运营</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">臧法收</div>
                            <div class="person-role">总经理、董事</div>
                            <div class="person-desc">主管生产与项目落地</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">李洪斗</div>
                            <div class="person-role">董事</div>
                            <div class="person-desc">参与重大业务布局</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">史新村</div>
                            <div class="person-role">董事</div>
                            <div class="person-desc">参与公司运营决策</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">仲伟华</div>
                            <div class="person-role">董事</div>
                            <div class="person-desc">主管技术领域</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">毕鹏良</div>
                            <div class="person-role">监事</div>
                            <div class="person-desc">负责财务与合规监督</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">张成磊</div>
                            <div class="person-role">监事（子公司）</div>
                            <div class="person-desc">主管安全生产与质量管理</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">韩红亮</div>
                            <div class="person-role">子公司董事长</div>
                            <div class="person-desc">负责科技板块业务</div>
                        </div>
                    </div>

                    <div class="card" style="margin-top: 20px;">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">实控人社会角色</div>
                                <div class="info-value">董鹏为实控人，其父董华友为创始人，已故</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">母公司画像</div>
                                <div class="info-value">弘润资管、弘润控股等多家子公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">对口业务部门负责人</div>
                                <div class="info-value">未提供具体对接人信息</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">所属主要行业主管条线</div>
                            <div class="info-value">石油、煤炭及其他燃料加工业</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要主管单位</div>
                            <div class="info-value">未明确提及</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">高端聚丙烯智慧化无人车间</span>
                            <span class="btn btn-info">聚酰亚胺全产业链数字化升级</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>合作方</th>
                                        <th>状态</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>锅炉智能优化控制系统</td>
                                        <td>与和利时集团合作</td>
                                        <td><span class="btn btn-success">已投运</span></td>
                                        <td>2024年</td>
                                    </tr>
                                    <tr>
                                        <td>安全生产信息化平台</td>
                                        <td>自研</td>
                                        <td><span class="btn btn-success">已验收运营</span></td>
                                        <td>2024年</td>
                                    </tr>
                                    <tr>
                                        <td>聚丙烯智慧化无人车间</td>
                                        <td>自研+AI技术集成</td>
                                        <td><span class="btn btn-success">已投产</span></td>
                                        <td>2024年</td>
                                    </tr>
                                    <tr>
                                        <td>设备预测性维护系统</td>
                                        <td>华为+自研</td>
                                        <td><span class="btn btn-warning">实施中</span></td>
                                        <td>2025年</td>
                                    </tr>
                                    <tr>
                                        <td>AI视频监控与环保中控系统</td>
                                        <td>华为+联通+云鼎科技</td>
                                        <td><span class="btn btn-warning">升级中</span></td>
                                        <td>2025年</td>
                                    </tr>
                                    <tr>
                                        <td>智能调度中心（建设中）</td>
                                        <td>华为联合开发</td>
                                        <td><span class="btn btn-info">规划已立项</span></td>
                                        <td>2025年</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="info-grid" style="margin-top: 16px;">
                            <div class="info-item">
                                <div class="info-label">已立项项目的推进阶段</div>
                                <div class="info-value">部分项目已投运，部分在建或规划中</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">待开发项目的商机价值评级</div>
                                <div class="info-value">未知</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">安全生产信息化平台</span>
                            <span class="btn btn-info">锅炉智能优化控制系统</span>
                            <span class="btn btn-success">聚丙烯智慧化无人车间</span>
                            <span class="btn btn-warning">设备预测性维护系统</span>
                            <span class="btn btn-secondary">AI视频监控与环保中控系统</span>
                            <span class="btn btn-primary">智能调度中心（建设中）</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 政策信息 -->
            <div id="section2" class="section" style="display: none;">
                <div class="section-title">二、政策信息（与企业关联紧密的相关政策）</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">关于印发《关于加快高新技术企业发展的实施意见》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市科学技术局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年9月12日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">高新技术企业发展支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>鼓励高新技术企业发展，提供税收优惠、审批绿色通道等支持措施。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">关于开展第三批省级产教融合型企业建设培育工作的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">济宁市发展和改革委员会</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-success">产教融合支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>主要内容：</strong>推动产教融合型企业建设，提供税收优惠、用地保障等支持。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">潍坊市科技局 潍坊市财政局关于印发《潍坊市科创引导基金管理办法》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市科技局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">科创基金支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #7b83b0;">
                            <strong>主要内容：</strong>设立科创引导基金，支持高新技术企业发展。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行业热点 -->
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>

                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>热点方向</th>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>潜在商机</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="btn btn-primary">数字化转型</span></td>
                                    <td>聚丙烯智慧化无人车间</td>
                                    <td>提升生产效率，降低运维成本</td>
                                    <td>推动更多智能化改造项目</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-success">绿色低碳</span></td>
                                    <td>灵活焦化工艺减碳</td>
                                    <td>降低碳排放，提升环保水平</td>
                                    <td>推动绿色技术应用</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-warning">新材料研发</span></td>
                                    <td>聚酰亚胺全产业链</td>
                                    <td>提升产品附加值，增强市场竞争力</td>
                                    <td>扩展新材料市场</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-info">智能制造</span></td>
                                    <td>AI视频监控与环保中控系统</td>
                                    <td>提升安全与环保管理水平</td>
                                    <td>推动智能制造应用</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 招投标信息 -->
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>

                <div class="subsection">
                    <div class="card" style="text-align: center; padding: 60px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                        <div style="font-size: 48px; color: #6c757d; margin-bottom: 16px;">📋</div>
                        <div style="font-size: 18px; color: #6c757d; font-weight: 500;">暂无相关信息</div>
                        <div style="font-size: 14px; color: #adb5bd; margin-top: 8px;">当前没有可显示的政采公示或招投标信息</div>
                    </div>
                </div>
            </div>

            <!-- 商机类型 -->
            <div id="section5" class="section" style="display: none;">
                <div class="section-title">五、商机类型</div>

                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>预算额度</th>
                                    <th>启动周期</th>
                                    <th>效果目标</th>
                                    <th>商机可能性评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智能调度中心建设</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>未公开</td>
                                    <td>2025年启动</td>
                                    <td>降低库存成本15%</td>
                                    <td><span class="score-badge score-high">0.98</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">化工垂域大模型开发</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>未公开</td>
                                    <td>2026年上线</td>
                                    <td>降低能耗15%</td>
                                    <td><span class="score-badge score-high">0.97</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">绿色低碳技术升级</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>未公开</td>
                                    <td>2025年深化</td>
                                    <td>年减碳42万吨</td>
                                    <td><span class="score-badge score-high">0.95</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">安全生产信息化平台扩展</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>未公开</td>
                                    <td>2025年深化</td>
                                    <td>拓展AI预测性维护系统</td>
                                    <td><span class="score-badge score-high">0.92</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">AI 2.0战略（智能调度中心）</div>
                                        <span class="btn btn-warning" style="font-size: 10px;">准显性商机</span>
                                    </td>
                                    <td>未公开</td>
                                    <td>2025年启动</td>
                                    <td>降低库存成本15%</td>
                                    <td><span class="score-badge score-high">0.96</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });

            // 显示选中的section
            document.getElementById(sectionId).style.display = 'block';

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');

            // 显示/隐藏二级导航
            const navSecondary = document.getElementById('nav-secondary');
            if (sectionId === 'section1') {
                navSecondary.classList.add('show');
            } else {
                navSecondary.classList.remove('show');
            }

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                const navHeight = document.querySelector('.nav-container').offsetHeight;
                const elementPosition = element.offsetTop - navHeight - 20;
                window.scrollTo({
                    top: elementPosition,
                    behavior: 'smooth'
                });
            }
        }

        // 初始化显示第一个section
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.display = index === 0 ? 'block' : 'none';
            });
            document.getElementById('nav-secondary').classList.add('show');

            // 添加平滑滚动效果
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const navHeight = document.querySelector('.nav-container').offsetHeight;
                        const targetPosition = target.offsetTop - navHeight - 20;
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // 添加鼠标悬停效果
            document.querySelectorAll('.card, .info-item, .person-card').forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
