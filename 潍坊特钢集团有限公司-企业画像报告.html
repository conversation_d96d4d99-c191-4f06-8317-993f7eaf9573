<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>潍坊特钢集团有限公司 - 企业画像报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: 16px;
            color: #6e6e73;
            margin-bottom: 0;
        }

        .navigation {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .nav-item {
            padding: 10px 20px;
            border-radius: 12px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        .nav-item.active {
            background: #4a90a4;
            color: white;
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
        }

        .sub-navigation {
            background: rgba(74, 144, 164, 0.05);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 20px;
            display: none;
            flex-wrap: wrap;
            gap: 6px;
            justify-content: center;
        }

        .sub-nav-item {
            padding: 6px 12px;
            border-radius: 8px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .sub-nav-item:hover {
            background: rgba(74, 144, 164, 0.1);
        }

        .sub-nav-item.active {
            background: #4a90a4;
            color: white;
        }

        .content-section {
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card h2 {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card h3 {
            font-size: 18px;
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 16px;
            margin-top: 24px;
        }

        .card h3:first-of-type {
            margin-top: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .info-value {
            color: #1d1d1f;
            font-size: 15px;
        }

        .rating-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #5ba3b4 50%, #6b73a0 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rating-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .rating-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .rating-stars {
            font-size: 20px;
            margin-bottom: 12px;
            color: #ffd700;
        }

        .rating-basis {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .rating-value {
            font-size: 14px;
            opacity: 0.8;
        }

        .person-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-role {
            font-size: 12px;
            opacity: 0.8;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #4a90a4;
            border-bottom: 2px solid #e9ecef;
            font-size: 14px;
        }

        td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 14px;
            vertical-align: top;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .score-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .score-high { background: #4a90a4; }
        .score-medium { background: #6b73a0; }
        .score-low { background: #8b93c0; }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6e6e73;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .rating-cards {
                grid-template-columns: 1fr;
            }
            
            .person-cards {
                grid-template-columns: 1fr;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>潍坊特钢集团有限公司</h1>
            <p>企业画像报告 | 生成时间：2025年1月10日</p>
        </div>

        <div class="navigation">
            <button class="nav-item active" onclick="showSection('customer-dynamics')">战客动态</button>
            <button class="nav-item" onclick="showSection('policy-info')">政策信息</button>
            <button class="nav-item" onclick="showSection('industry-trends')">行业热点</button>
            <button class="nav-item" onclick="showSection('bidding-info')">招投标信息</button>
            <button class="nav-item" onclick="showSection('business-opportunities')">商机类型</button>
        </div>

        <div class="sub-navigation" id="customer-dynamics-subnav">
            <button class="sub-nav-item active" onclick="showSubSection('basic-info')">基础信息</button>
            <button class="sub-nav-item" onclick="showSubSection('value-rating')">价值度评级</button>
            <button class="sub-nav-item" onclick="showSubSection('business-profile')">经营画像</button>
            <button class="sub-nav-item" onclick="showSubSection('industry-profile')">行业画像</button>
            <button class="sub-nav-item" onclick="showSubSection('key-personnel')">关键人</button>
            <button class="sub-nav-item" onclick="showSubSection('supervisory-unit')">主管单位</button>
            <button class="sub-nav-item" onclick="showSubSection('key-projects')">重点项目</button>
        </div>
