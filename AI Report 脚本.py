#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Report 企业画像报告生成脚本 v2.0
标准化文档内容提取和验证工具

使用方法：
python3 "AI Report 脚本.py" --extract "文档路径.docx"
python3 "AI Report 脚本.py" --validate "文档路径.docx"
python3 "AI Report 脚本.py" --key-persons "文档路径.docx"
"""

import docx
import sys
import os
import argparse
import json
from datetime import datetime

class DocumentExtractor:
    """文档内容提取器"""
    
    def __init__(self, doc_path):
        self.doc_path = doc_path
        self.doc = None
        self.paragraphs = []
        self.tables = []
        self.extraction_results = {}
        
    def validate_document(self):
        """验证文档是否存在且格式正确"""
        if not os.path.exists(self.doc_path):
            print(f"❌ 文件不存在: {self.doc_path}")
            return False
            
        if not self.doc_path.endswith('.docx'):
            print(f"❌ 文件格式错误，需要.docx格式: {self.doc_path}")
            return False
            
        try:
            self.doc = docx.Document(self.doc_path)
            print(f"✅ 文档验证通过: {self.doc_path}")
            return True
        except Exception as e:
            print(f"❌ 文档打开失败: {e}")
            return False
    
    def extract_paragraphs(self):
        """提取所有段落"""
        print("=== 提取段落内容 ===")
        
        self.paragraphs = []
        for i, para in enumerate(self.doc.paragraphs):
            text = para.text.strip()
            if text:
                self.paragraphs.append({
                    'index': i + 1,
                    'text': text
                })
        
        print(f"总段落数: {len(self.paragraphs)}")
        
        # 输出前10个段落作为预览
        print("\n前10个段落预览:")
        for para in self.paragraphs[:10]:
            print(f"{para['index']:3d}: {para['text'][:100]}{'...' if len(para['text']) > 100 else ''}")
        
        if len(self.paragraphs) > 10:
            print(f"... 还有 {len(self.paragraphs) - 10} 个段落")
            
        return self.paragraphs
    
    def extract_tables(self):
        """提取所有表格"""
        print("\n=== 提取表格内容 ===")
        
        self.tables = []
        for i, table in enumerate(self.doc.tables):
            table_data = {
                'index': i + 1,
                'rows': len(table.rows),
                'columns': len(table.columns) if table.rows else 0,
                'data': []
            }
            
            for j, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_data.append(cell_text)
                table_data['data'].append(row_data)
            
            self.tables.append(table_data)
            
            print(f"\n表格 {i+1} ({table_data['rows']}行 x {table_data['columns']}列):")
            
            # 输出表头
            if table_data['data']:
                headers = table_data['data'][0]
                print(f"  表头: {' | '.join(headers)}")
                
                # 判断是否为商机表格
                header_text = ' '.join(headers)
                if any(keyword in header_text for keyword in ['商机', '场景', '方案', '痛点', '竞争', '评分']):
                    print(f"  🎯 识别为商机表格")
                    print(f"  📊 数据行数: {len(table_data['data'])-1}行")
        
        print(f"\n总表格数: {len(self.tables)}")
        return self.tables
    
    def validate_basic_info(self):
        """验证基础信息完整性"""
        print("\n=== 基础信息验证 ===")
        
        basic_keywords = [
            '企业名称', '成立日期', '注册资金', '经营地址', 
            '企业属性', '实缴资金', '隶属关系', '分支机构'
        ]
        
        found_info = {}
        missing_info = []
        
        for keyword in basic_keywords:
            found = False
            for para in self.paragraphs:
                if keyword in para['text']:
                    found_info[keyword] = {
                        'line': para['index'],
                        'content': para['text']
                    }
                    found = True
                    break
            
            if not found:
                missing_info.append(keyword)
        
        print(f"✅ 找到基础信息: {len(found_info)}/8")
        for keyword, info in found_info.items():
            print(f"  {keyword}: 第{info['line']}行")
        
        if missing_info:
            print(f"❌ 缺失基础信息: {missing_info}")
        
        return found_info, missing_info
    
    def validate_value_rating(self):
        """验证价值度评级"""
        print("\n=== 价值度评级验证 ===")
        
        rating_keywords = ['品牌价值', '收益价值', '合作紧密度']
        star_patterns = ['★', '星']
        
        found_ratings = {}
        missing_ratings = []
        
        for keyword in rating_keywords:
            found = False
            for para in self.paragraphs:
                if keyword in para['text']:
                    # 查找对应的星级评分
                    star_line = None
                    for star_para in self.paragraphs:
                        if any(star in star_para['text'] for star in star_patterns):
                            if abs(star_para['index'] - para['index']) <= 3:  # 在附近3行内
                                star_line = star_para['index']
                                break
                    
                    found_ratings[keyword] = {
                        'line': para['index'],
                        'content': para['text'],
                        'star_line': star_line
                    }
                    found = True
                    break
            
            if not found:
                missing_ratings.append(keyword)
        
        print(f"✅ 找到价值评级: {len(found_ratings)}/3")
        for keyword, info in found_ratings.items():
            star_status = f"(星级: 第{info['star_line']}行)" if info['star_line'] else "(⚠️ 未找到星级)"
            print(f"  {keyword}: 第{info['line']}行 {star_status}")
        
        if missing_ratings:
            print(f"❌ 缺失价值评级: {missing_ratings}")
        
        return found_ratings, missing_ratings
    
    def extract_key_persons(self):
        """提取关键人信息"""
        print("\n=== 关键人信息提取 ===")
        
        # 扩展关键词列表，适应不同行业
        keywords = [
            '院长', '副院长', '书记', '主任', '纪委书记', '党委书记',
            '董事长', '总经理', '副总', '总裁', '经理', '主管', 
            '局长', '副局长', '处长', '科长', '负责人', '联系人'
        ]
        
        key_person_paragraphs = []
        persons = []
        
        # 查找关键人相关段落
        for para in self.paragraphs:
            text = para['text']
            if any(keyword in text for keyword in keywords + ['关键人', '负责人', '对接人', '联系方式', '分管', '主持']):
                key_person_paragraphs.append(para)
        
        print(f"找到关键人相关段落: {len(key_person_paragraphs)}个")
        
        # 解析人员信息
        for para in key_person_paragraphs:
            text = para['text']
            
            # 查找包含姓名和职务的段落（支持多种格式）
            if '：' in text and any(title in text for title in keywords):
                # 分析格式：姓名：职务，分工描述
                parts = text.split('：')
                if len(parts) >= 2:
                    name = parts[0].strip()
                    rest = '：'.join(parts[1:]).strip()
                    
                    # 进一步分析职务和描述
                    if '，' in rest:
                        title_desc = rest.split('，')
                        title = title_desc[0].strip()
                        desc = '，'.join(title_desc[1:]).strip()
                    else:
                        title = rest
                        desc = ""
                    
                    # 过滤掉明显不是人名的内容
                    if not any(exclude in name for exclude in ['实控人', '社会角色', '母公司', '对接人']):
                        persons.append({
                            'name': name,
                            'title': title,
                            'desc': desc,
                            'source_line': para['index']
                        })
        
        print(f"✅ 提取到关键人员: {len(persons)}人")
        for i, person in enumerate(persons, 1):
            print(f"  {i}. {person['name']} - {person['title']} (第{person['source_line']}行)")
        
        # 查找其他相关信息
        other_info = {}
        for para in self.paragraphs:
            text = para['text']
            if '实控人社会角色：' in text:
                other_info['实控人社会角色'] = {'line': para['index'], 'content': text}
            elif '母公司画像：' in text:
                other_info['母公司画像'] = {'line': para['index'], 'content': text}
            elif '对口业务部门负责人' in text:
                other_info['对接人信息'] = {'line': para['index'], 'content': text}
        
        if other_info:
            print("\n📋 其他相关信息:")
            for key, info in other_info.items():
                print(f"  {key}: 第{info['line']}行")
        
        return persons, other_info
    
    def validate_business_tables(self):
        """验证商机表格"""
        print("\n=== 商机表格验证 ===")
        
        business_tables = []
        
        for table in self.tables:
            if table['data']:
                headers = table['data'][0]
                header_text = ' '.join(headers)
                
                if any(keyword in header_text for keyword in ['商机', '场景', '方案', '痛点', '竞争', '评分']):
                    business_tables.append({
                        'index': table['index'],
                        'headers': headers,
                        'data_rows': len(table['data']) - 1,
                        'data': table['data'][1:]  # 排除表头
                    })
        
        print(f"✅ 找到商机表格: {len(business_tables)}个")
        
        for table in business_tables:
            print(f"  表格{table['index']}: {table['data_rows']}行商机数据")
            print(f"    表头: {' | '.join(table['headers'])}")
            
            # 检查评分格式
            score_column = -1
            for i, header in enumerate(table['headers']):
                if '评分' in header or '分数' in header:
                    score_column = i
                    break
            
            if score_column >= 0:
                print(f"    评分列: 第{score_column+1}列")
                scores = [row[score_column] if score_column < len(row) else '' for row in table['data']]
                print(f"    评分值: {scores}")
        
        return business_tables
    
    def generate_summary_report(self):
        """生成提取结果摘要报告"""
        print("\n" + "="*60)
        print("📊 文档提取结果摘要报告")
        print("="*60)
        
        # 基础统计
        basic_info, missing_basic = self.validate_basic_info()
        ratings, missing_ratings = self.validate_value_rating()
        persons, other_info = self.extract_key_persons()
        business_tables = self.validate_business_tables()
        
        # 生成摘要
        summary = {
            'document_path': self.doc_path,
            'extraction_time': datetime.now().isoformat(),
            'statistics': {
                'total_paragraphs': len(self.paragraphs),
                'total_tables': len(self.tables),
                'basic_info_found': len(basic_info),
                'basic_info_missing': len(missing_basic),
                'value_ratings_found': len(ratings),
                'value_ratings_missing': len(missing_ratings),
                'key_persons_found': len(persons),
                'business_tables_found': len(business_tables)
            },
            'quality_score': self._calculate_quality_score(basic_info, ratings, persons, business_tables),
            'missing_items': {
                'basic_info': missing_basic,
                'value_ratings': missing_ratings
            },
            'recommendations': self._generate_recommendations(missing_basic, missing_ratings, persons, business_tables)
        }
        
        # 输出摘要
        print(f"📄 文档: {os.path.basename(self.doc_path)}")
        print(f"📊 段落数: {summary['statistics']['total_paragraphs']}")
        print(f"📋 表格数: {summary['statistics']['total_tables']}")
        print(f"✅ 基础信息: {summary['statistics']['basic_info_found']}/8")
        print(f"⭐ 价值评级: {summary['statistics']['value_ratings_found']}/3")
        print(f"👥 关键人员: {summary['statistics']['key_persons_found']}人")
        print(f"💼 商机表格: {summary['statistics']['business_tables_found']}个")
        print(f"🎯 质量评分: {summary['quality_score']}/100")
        
        if summary['recommendations']:
            print(f"\n💡 建议:")
            for rec in summary['recommendations']:
                print(f"  • {rec}")
        
        return summary
    
    def _calculate_quality_score(self, basic_info, ratings, persons, business_tables):
        """计算质量评分"""
        score = 0
        
        # 基础信息 (40分)
        score += (len(basic_info) / 8) * 40
        
        # 价值评级 (30分)
        score += (len(ratings) / 3) * 30
        
        # 关键人员 (20分)
        if len(persons) >= 3:
            score += 20
        elif len(persons) >= 1:
            score += 10
        
        # 商机表格 (10分)
        if len(business_tables) >= 1:
            score += 10
        
        return round(score, 1)
    
    def _generate_recommendations(self, missing_basic, missing_ratings, persons, business_tables):
        """生成改进建议"""
        recommendations = []
        
        if missing_basic:
            recommendations.append(f"补充缺失的基础信息: {', '.join(missing_basic)}")
        
        if missing_ratings:
            recommendations.append(f"补充缺失的价值评级: {', '.join(missing_ratings)}")
        
        if len(persons) == 0:
            recommendations.append("未找到关键人信息，请检查文档格式")
        
        if len(business_tables) == 0:
            recommendations.append("未找到商机表格，请确认文档包含商机数据")
        
        return recommendations

def main():
    parser = argparse.ArgumentParser(description='AI Report 企业画像报告生成脚本 v2.0')
    parser.add_argument('--extract', type=str, help='提取文档完整内容')
    parser.add_argument('--validate', type=str, help='验证文档格式和基础信息')
    parser.add_argument('--key-persons', type=str, help='专门提取关键人信息')
    
    args = parser.parse_args()
    
    if not any([args.extract, args.validate, args.key_persons]):
        parser.print_help()
        return
    
    doc_path = args.extract or args.validate or args.key_persons
    
    print("🚀 AI Report 企业画像报告生成脚本 v2.0")
    print(f"📄 处理文档: {doc_path}")
    print("-" * 60)
    
    extractor = DocumentExtractor(doc_path)
    
    # 验证文档
    if not extractor.validate_document():
        return
    
    if args.validate:
        # 仅验证模式
        extractor.extract_paragraphs()
        extractor.extract_tables()
        extractor.generate_summary_report()
    
    elif args.extract:
        # 完整提取模式
        extractor.extract_paragraphs()
        extractor.extract_tables()
        summary = extractor.generate_summary_report()
        
        # 保存提取结果
        output_file = doc_path.replace('.docx', '_extraction_results.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print(f"\n💾 提取结果已保存: {output_file}")
    
    elif args.key_persons:
        # 关键人专门提取模式
        extractor.extract_paragraphs()
        persons, other_info = extractor.extract_key_persons()

if __name__ == "__main__":
    main()
