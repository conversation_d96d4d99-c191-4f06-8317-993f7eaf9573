<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：潍坊特钢集团有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 内容区域 */
        .content {
            padding: 0 30px 30px;
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 30px 0 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 30px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subsection-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #4a90a4;
            border-radius: 2px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .info-value {
            color: #1d1d1f;
            font-size: 14px;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rating-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .rating-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .rating-stars {
            font-size: 20px;
            color: #ffd700;
        }

        .rating-details {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 按钮组 */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 卡片 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-role {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 表格 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #4a90a4;
            border-bottom: 2px solid #e9ecef;
            font-size: 14px;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 14px;
            vertical-align: top;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 评分徽章 */
        .score-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .score-high { background: #4a90a4; }
        .score-medium { background: #6b73a0; }
        .score-low { background: #8b93c0; }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">商机类型</button>
            </div>
            
            <div class="nav-secondary show">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>企业画像报告</h1>
            <div class="subtitle">潍坊特钢集团有限公司</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 战客动态 -->
            <div id="section1" class="section">
                <div class="section-title">一、战客动态</div>

                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">企业名称（简称）</div>
                            <div class="info-value">潍坊特钢集团有限公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">成立日期</div>
                            <div class="info-value">1993年11月09日</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">企业属性</div>
                            <div class="info-value">其他有限责任公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">注册资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实缴资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">经营地址</div>
                            <div class="info-value">山东省潍坊市钢厂工业园潍钢东路</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">隶属关系</div>
                            <div class="info-value">山东永锋控股集团有限公司（2024年持股52%）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要分支机构</div>
                            <div class="info-value">安丘分公司、山东经纬钢帘线公司等</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>入选《2023年5G工厂名录》，在区域钢铁行业中具有一定的品牌影响力。<br>
                                <strong>价值点：</strong>在高端钢材领域有较强的产品竞争力，是山东省循环经济示范企业。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>企业营收规模达301.61亿元（2023年），是山东省重点钢铁企业之一。<br>
                                <strong>价值点：</strong>拥有较强的市场占有率和稳定的客户群体，具备较大的潜在合作空间。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>与移动通信运营商有合作项目，如5G车间建设项目、移动专线扩容项目等。历史项目交付质量良好，物流EBC系统、5G车间等项目获得行业认可。<br>
                                <strong>价值点：</strong>具备良好的合作基础和履约能力，适合进一步深化合作。
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">特钢棒材（合金结构钢、轴承钢、管坯钢等）、高速线材（胎圈钢丝用钢、钢帘线用钢等）、金属制品（预应力钢绞线、镀锌钢丝等）、新型建材（蒸压加气混凝土砌块、岩棉保温材料等）、能源服务（余热发电、工业供暖等）、环保服务（工业废水处理、固废资源化等）、物流运输（铁路专用线运输、公路货运等）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">上游为铁矿石、焦炭等原材料供应，中游为钢铁冶炼和加工，下游为汽车制造、工程机械、基建等应用领域</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">2023年营收301.61亿元，位列中国民营企业500强第458位</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群</span>
                        <span class="btn btn-success">规上企业</span>
                        <span class="btn btn-success">专精特新企业</span>
                        <span class="btn btn-secondary">非省级单项冠军</span>
                    </div>
                </div>

                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">在区域内具有较高的市场份额，是山东省重要的钢铁生产企业之一</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">入选《2023年5G工厂名录》，在智能制造领域处于行业前列</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">与北京科技大学共建联合研究中心，技术研发实力较强</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">中国民营企业500强第490位</span>
                            <span class="btn btn-success">国家级绿色工厂</span>
                            <span class="btn btn-warning">山东省资源综合利用先进单位</span>
                            <span class="btn btn-info">中国制造业企业500强第390位</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">资质认证</div>
                            <div class="info-value">
                                <span class="btn btn-info">规上企业</span>
                                <span class="btn btn-info">专精特新企业</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专利商标统计情况</div>
                            <div class="info-value">未公开具体数据，但企业在技术研发方面投入较大，与北京科技大学共建联合研究中心，参与多项技术攻关</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div class="person-card">
                            <div class="person-name">于光富</div>
                            <div class="person-role">法定代表人</div>
                            <div class="person-desc">负责企业法律事务和重大决策</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">董和玉</div>
                            <div class="person-role">董事长</div>
                            <div class="person-desc">负责企业战略规划和经营管理</div>
                        </div>
                    </div>

                    <div class="card" style="margin-top: 20px;">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">实控人社会角色</div>
                                <div class="info-value">于光富（法人代表）、董和玉（董事长）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">母公司画像</div>
                                <div class="info-value">山东永锋控股集团有限公司，持股52%，为大型综合性企业集团</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">对口业务部门负责人</div>
                                <div class="info-value">未公开具体信息</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">所属主要行业主管条线</div>
                            <div class="info-value">工业和信息化、生态环境、科技等部门</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要主管单位</div>
                            <div class="info-value">山东省工业和信息化厅、潍坊市工业和信息化局、潍坊市生态环境局等</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">京沪高铁/郑万高铁钢绞线供应项目</span>
                            <span class="btn btn-success">潍柴国际配套产业园高端汽车用钢项目</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>京沪高铁/郑万高铁钢绞线供应项目：</strong>提供高强度预应力钢绞线，应用于国家"八纵八横"高铁干线。<br><br>
                            <strong>潍柴国际配套产业园高端汽车用钢项目：</strong>建设KOCKS减定径机组及精整探伤线，年产30万吨汽车齿轮钢/曲轴用钢。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>项目描述</th>
                                        <th>预期效果</th>
                                        <th>合作方</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>5G车间建设项目</td>
                                        <td>与中国移动合作，实现钢包跟踪、行车远程操控</td>
                                        <td>提升生产效率</td>
                                        <td>中国移动</td>
                                    </tr>
                                    <tr>
                                        <td>物流EBC系统项目</td>
                                        <td>部署物流数字化管理平台，覆盖采购、销售、厂内物流全流程</td>
                                        <td>年降本超3000万元</td>
                                        <td>自主建设</td>
                                    </tr>
                                    <tr>
                                        <td>80万吨焊丝胎圈金属制品项目</td>
                                        <td>年产胎圈钢丝/钢帘线80万吨，产品最细直径0.175mm（头发丝级）</td>
                                        <td>提升高端产品产能</td>
                                        <td>自主建设</td>
                                    </tr>
                                    <tr>
                                        <td>循环经济产业链建设项目</td>
                                        <td>建成钢渣微粉、余热发电、污水处理系统</td>
                                        <td>固废利用率100%</td>
                                        <td>自主建设</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>技术方案</th>
                                        <th>应用场景</th>
                                        <th>效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>5G车间</td>
                                        <td>基于5G网络</td>
                                        <td>钢包跟踪、行车远程操控</td>
                                        <td>提升生产效率</td>
                                    </tr>
                                    <tr>
                                        <td>物流EBC系统</td>
                                        <td>数字化管理平台</td>
                                        <td>采购、销售、厂内物流全流程数字化管控</td>
                                        <td>年降本超3000万元</td>
                                    </tr>
                                    <tr>
                                        <td>能源管控平台</td>
                                        <td>能源数据集成与优化</td>
                                        <td>能源消耗监控和优化</td>
                                        <td>年减排CO₂ 13万吨</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行业热点 -->
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>
                <div class="subsection">
                    <div class="card">
                        <div class="card-title">热点方向</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">智能制造</span>
                            <span class="btn btn-success">绿色低碳</span>
                            <span class="btn btn-warning">数字化转型</span>
                            <span class="btn btn-info">5G+工业互联网</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">案例</div>
                        <div style="margin-bottom: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>5G车间：</strong>与中国移动合作，实现钢包跟踪、行车远程操控，提升生产效率15%。
                        </div>
                        <div style="margin-bottom: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>物流EBC系统：</strong>部署物流数字化管理平台，覆盖采购、销售、厂内物流全流程，年降本超3000万元。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">潜在商机</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">工业互联网平台建设</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">5G+工业互联网解决方案</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">数字化转型项目（如MES系统、ERP系统）</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">信息来源</div>
                        <div style="padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b5a9c;">
                            <strong>来源：</strong>工信部《2023年5G工厂名录》、潍坊工信局公告、兰格数科物流EBC系统项目。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 招投标信息 -->
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>标讯标题</th>
                                    <th>招标方式</th>
                                    <th>招标公司</th>
                                    <th>产品</th>
                                    <th>中标公司</th>
                                    <th>项目地区</th>
                                    <th>金额</th>
                                    <th>发布日期</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>移动专线扩容项目</td>
                                    <td>公开招标</td>
                                    <td>中国招标与采购网</td>
                                    <td>移动专线</td>
                                    <td>未公开</td>
                                    <td>潍坊</td>
                                    <td>未公开</td>
                                    <td>2023年6月</td>
                                </tr>
                                <tr>
                                    <td>5G车间建设项目</td>
                                    <td>公开招标</td>
                                    <td>未公开</td>
                                    <td>5G车间</td>
                                    <td>中国移动</td>
                                    <td>潍坊</td>
                                    <td>未公开</td>
                                    <td>2023年</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 政策信息 -->
            <div id="section2" class="section" style="display: none;">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">关于印发《关于加快高新技术企业发展的实施意见》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">潍科发〔2024〕12号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市科学技术局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年9月12日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">高新技术企业发展扶持政策</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>支持高新技术企业发展，鼓励企业加大研发投入，推动科技创新，提供税收优惠、审批绿色通道等政策支持。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">关于印发《潍坊市工业数字化转型和智能化改造三年行动方案（2023-2025年）》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">潍政办字〔2023〕8号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市人民政府办公厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2023年8月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-success">工业数字化转型政策</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>主要内容：</strong>推动4000家规上工业企业实施数转智改，支持5G+工业互联网示范项目，给予最高500万元补贴。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">关于印发《潍坊市支持工业数字化转型和智能化改造十条政策》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">潍工信发〔2024〕15号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市工业和信息化局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年9月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-warning">工业数字化转型扶持政策</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>主要内容：</strong>对数字化转型项目按投资额20%补贴，最高200万元；对"揭榜挂帅"项目最高补贴100万元。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">国家级政策</div>

                    <div class="card">
                        <div class="card-title">《钢铁行业规范条件(2025年版)》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工业和信息化部</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2025年1月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-primary">钢铁行业规范发展政策</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>建立"规范企业"和"引领型规范企业"两级评价体系，推动钢铁行业向智能制造、绿色低碳方向发展。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">《原材料工业数字化转型工作方案（2024—2026年）》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工信部等九部门</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年1月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-secondary">原材料工业数字化转型政策</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b93c0;">
                            <strong>主要内容：</strong>推动钢铁行业关键工序数控化率达到80%，培育30家以上智能工厂，支持AI、大数据在工艺优化、质量预测中的应用。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">《山东省钢铁工业高质量发展行动计划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省工业和信息化厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">钢铁行业发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #7b83b0;">
                            <strong>主要内容：</strong>推动钢铁企业智能化改造，支持5G工厂、工业互联网平台建设，2025年规模以上工业企业关键工序数控化率超75%。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">《关于推动传统产业优化升级培育发展新质生产力的实施方案》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府办公厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年12月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-warning">传统产业转型升级政策</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>主要内容：</strong>实施制造业数字化转型提标行动，推进"智能制造""智慧能源"建设，支持钢铁行业开展节能降碳技术改造。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商机类型 -->
            <div id="section5" class="section" style="display: none;">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机领域</th>
                                    <th>潜在解决方案</th>
                                    <th>客户价值</th>
                                    <th>实施难度</th>
                                    <th>商机评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">工业互联网平台</div>
                                        <span class="btn btn-primary" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>搭建5G+边缘计算平台，整合生产、能源、排放数据，实现全流程可视化与AI优化调度</td>
                                    <td>提升良率10%+，降低能耗15%，满足ESG数据追溯需求</td>
                                    <td>中高（需跨系统集成）</td>
                                    <td><span class="score-badge score-high">0.85</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">AI质检与远程运维</div>
                                        <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                    </td>
                                    <td>部署5G+机器视觉系统，实现废钢智能判级、棒材表面缺陷实时检测；AR远程维护降低停机时间</td>
                                    <td>减少人工成本30%，提升质检效率50%，保障高端客户交付质量</td>
                                    <td>中（技术成熟度高）</td>
                                    <td><span class="score-badge score-medium">0.80</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智慧能源管理</div>
                                        <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                    </td>
                                    <td>基于物联网的能源管控平台，动态调节余热发电、煤气回收（如转炉煤气回收量提升至130m³/吨钢）</td>
                                    <td>年节煤5万吨+减碳13万吨，经济效益超3000万/年</td>
                                    <td>低（已有基础）</td>
                                    <td><span class="score-badge score-high">0.90</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">供应链协同云</div>
                                        <span class="btn btn-warning" style="font-size: 10px;">预判商机</span>
                                    </td>
                                    <td>定制化云平台链接潍柴等客户，共享订单进度、质检报告、物流信息，支持JIT生产</td>
                                    <td>缩短交付周期20%，增强产业链黏性，助力高端市场拓展</td>
                                    <td>中（需客户协同）</td>
                                    <td><span class="score-badge score-medium">0.75</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">ESG数字化平台</div>
                                        <span class="btn btn-info" style="font-size: 10px;">观测商机</span>
                                    </td>
                                    <td>构建碳足迹追踪系统，覆盖原料采购至产品交付，自动生成EcoVadis认证报告</td>
                                    <td>满足国际供应链审计，提升出口竞争力，规避贸易壁垒</td>
                                    <td>高（标准复杂）</td>
                                    <td><span class="score-badge score-low">0.70</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });

            // 显示选中的section
            document.getElementById(sectionId).style.display = 'block';

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');

            // 控制子导航显示
            const subNav = document.querySelector('.nav-secondary');
            if (sectionId === 'section1') {
                subNav.classList.add('show');
            } else {
                subNav.classList.remove('show');
            }

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示第一个section
            document.getElementById('section1').style.display = 'block';

            // 添加涟漪效果
            const buttons = document.querySelectorAll('.nav-item, .nav-sub-item, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // 添加涟漪效果的CSS
        const style = document.createElement('style');
        style.textContent = `
            .nav-item, .nav-sub-item, .btn {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
