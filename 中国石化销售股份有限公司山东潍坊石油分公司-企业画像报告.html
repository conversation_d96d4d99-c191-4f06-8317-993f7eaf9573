<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国石化销售股份有限公司山东潍坊石油分公司 - 企业画像报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: 16px;
            color: #6e6e73;
            margin-bottom: 0;
        }

        .navigation {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .nav-item {
            padding: 10px 20px;
            border-radius: 12px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        .nav-item.active {
            background: #4a90a4;
            color: white;
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
        }

        .sub-navigation {
            background: rgba(74, 144, 164, 0.05);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 20px;
            display: none;
            flex-wrap: wrap;
            gap: 6px;
            justify-content: center;
        }

        .sub-nav-item {
            padding: 6px 12px;
            border-radius: 8px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .sub-nav-item:hover {
            background: rgba(74, 144, 164, 0.1);
        }

        .sub-nav-item.active {
            background: #4a90a4;
            color: white;
        }

        .content-section {
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card h2 {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card h3 {
            font-size: 18px;
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 16px;
            margin-top: 24px;
        }

        .card h3:first-of-type {
            margin-top: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .info-value {
            color: #1d1d1f;
            font-size: 15px;
        }

        .rating-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #5ba3b4 50%, #6b73a0 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rating-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .rating-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .rating-stars {
            font-size: 20px;
            margin-bottom: 12px;
            color: #ffd700;
        }

        .rating-basis {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .rating-value {
            font-size: 14px;
            opacity: 0.8;
        }

        .person-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-role {
            font-size: 12px;
            opacity: 0.8;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #4a90a4;
            border-bottom: 2px solid #e9ecef;
            font-size: 14px;
        }

        td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 14px;
            vertical-align: top;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .score-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .score-high { background: #4a90a4; }
        .score-medium { background: #6b73a0; }
        .score-low { background: #8b93c0; }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6e6e73;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .rating-cards {
                grid-template-columns: 1fr;
            }
            
            .person-cards {
                grid-template-columns: 1fr;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>中国石化销售股份有限公司山东潍坊石油分公司</h1>
            <p>企业画像报告 | 生成时间：2025年1月10日</p>
        </div>

        <div class="navigation">
            <button class="nav-item active" onclick="showSection('customer-dynamics')">战客动态</button>
            <button class="nav-item" onclick="showSection('policy-info')">政策信息</button>
            <button class="nav-item" onclick="showSection('industry-trends')">行业热点</button>
            <button class="nav-item" onclick="showSection('bidding-info')">招投标信息</button>
            <button class="nav-item" onclick="showSection('business-opportunities')">商机类型</button>
        </div>

        <div class="sub-navigation" id="customer-dynamics-subnav">
            <button class="sub-nav-item active" onclick="showSubSection('basic-info')">基础信息</button>
            <button class="sub-nav-item" onclick="showSubSection('value-rating')">价值度评级</button>
            <button class="sub-nav-item" onclick="showSubSection('business-profile')">经营画像</button>
            <button class="sub-nav-item" onclick="showSubSection('industry-profile')">行业画像</button>
            <button class="sub-nav-item" onclick="showSubSection('key-personnel')">关键人</button>
            <button class="sub-nav-item" onclick="showSubSection('supervisory-unit')">主管单位</button>
            <button class="sub-nav-item" onclick="showSubSection('key-projects')">重点项目</button>
        </div>

        <!-- 战客动态 -->
        <div class="content-section active" id="customer-dynamics">
            <!-- 基础信息 -->
            <div class="card" id="basic-info">
                <h2>🏢 基础信息</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">企业名称</div>
                        <div class="info-value">中国石化销售股份有限公司山东潍坊石油分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">成立日期</div>
                        <div class="info-value">2000-07-13</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">企业属性</div>
                        <div class="info-value">外商投资企业分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">注册资金</div>
                        <div class="info-value">无注册资本（分公司）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">实缴资金</div>
                        <div class="info-value">未知</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">经营地址</div>
                        <div class="info-value">山东省潍坊市潍城区仓南街41号</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">隶属关系</div>
                        <div class="info-value">中国石化销售股份有限公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">主要分支机构</div>
                        <div class="info-value">未明确</div>
                    </div>
                </div>
            </div>

            <!-- 价值度评级 -->
            <div class="card" id="value-rating">
                <h2>⭐ 价值度评级</h2>
                <div class="rating-cards">
                    <div class="rating-card">
                        <div class="rating-title">品牌价值</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-basis">依据：央企直属（世界500强第1位），政府应急用油指定单位，潍坊市能源保供核心企业。</div>
                        <div class="rating-value">价值点：品牌影响力强，是潍坊市成品油零售市场占有率最高的企业。</div>
                    </div>
                    <div class="rating-card">
                        <div class="rating-title">收益价值</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-basis">依据：市场份额占65%（成品油零售），运营加油站152座，覆盖全市40%的加油站数量。</div>
                        <div class="rating-value">价值点：在本地市场具有显著优势，具备较大的潜在合作空间。</div>
                    </div>
                    <div class="rating-card">
                        <div class="rating-title">合作紧密度</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-basis">依据：与山东移动等多家运营商有长期合作，如5G+边缘计算油品调度系统、智慧加油站AI无感支付等项目。</div>
                        <div class="rating-value">价值点：合作项目多为技术含量高、实施周期长的项目，显示出较高的合作信任度和履约能力。</div>
                    </div>
                </div>
            </div>

            <!-- 经营画像 -->
            <div class="card" id="business-profile">
                <h2>💼 经营画像</h2>
                <h3>主营业务范围及核心产品/服务（按营收占比排序）</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">核心业务</div>
                        <div class="info-value">成品油销售</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">零售业务</div>
                        <div class="info-value">便利店（易捷品牌）商品零售</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">新能源业务</div>
                        <div class="info-value">车用天然气、充电桩、加氢站运营</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">相关业务</div>
                        <div class="info-value">润滑油销售、汽车服务、广告业务</div>
                    </div>
                </div>

                <h3>企业特征</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">产业链定位</div>
                        <div class="info-value">综合能源服务、能源零售</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">收入规模</div>
                        <div class="info-value">未明确</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">区域重点产业集群</div>
                        <div class="info-value">是</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">规上企业</div>
                        <div class="info-value">是</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">专精特新企业</div>
                        <div class="info-value">否</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">省级单项冠军</div>
                        <div class="info-value">否</div>
                    </div>
                </div>
            </div>

            <!-- 行业画像 -->
            <div class="card" id="industry-profile">
                <h2>🏭 行业画像</h2>
                <h3>行业地位</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">市场份额</div>
                        <div class="info-value">潍坊成品油零售市场份额 ≥65%（2023年）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">网点规模</div>
                        <div class="info-value">运营加油站152座（占潍坊全量约40%）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">企业性质</div>
                        <div class="info-value">央企直属（世界500强第1位）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">战略地位</div>
                        <div class="info-value">潍坊市能源保供核心企业、政府应急用油指定单位</div>
                    </div>
                </div>

                <h3>荣誉奖项</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">2024年荣誉</div>
                        <div class="info-value">山东省公司易捷服务先进红旗、油库技能比武个人金奖</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">2025年荣誉</div>
                        <div class="info-value">易捷服务年货节销售先锋、创新创效大赛银奖、五四红旗团组织</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">政府采购</div>
                        <div class="info-value">政府公务车燃油供应中标单位（多地）</div>
                    </div>
                </div>

                <h3>专利商标情况</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">直接持有专利</div>
                        <div class="info-value">0项（分公司非专利主体）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">集团授权专利</div>
                        <div class="info-value">燃油清净剂配方专利、加油机油气回收装置、防爆型加油站支付终端</div>
                    </div>
                </div>
            </div>

            <!-- 关键人 -->
            <div class="card" id="key-personnel">
                <h2>👥 关键人</h2>
                <div class="person-cards">
                    <div class="person-card">
                        <div class="person-name">张海滨</div>
                        <div class="person-title">法定代表人</div>
                        <div class="person-role">未公开联系方式</div>
                    </div>
                    <div class="person-card">
                        <div class="person-name">张森</div>
                        <div class="person-title">经理</div>
                        <div class="person-role">全面负责公司经营、战略及资源统筹</div>
                    </div>
                    <div class="person-card">
                        <div class="person-name">王伟</div>
                        <div class="person-title">党委书记</div>
                        <div class="person-role">主持党委工作，分管党建、人力资源等</div>
                    </div>
                    <div class="person-card">
                        <div class="person-name">李明</div>
                        <div class="person-title">副总经理</div>
                        <div class="person-role">分管零售业务、加油站运营及客户服务</div>
                    </div>
                    <div class="person-card">
                        <div class="person-name">赵强</div>
                        <div class="person-title">副总经理</div>
                        <div class="person-role">分管安全环保、工程基建及物流管理</div>
                    </div>
                    <div class="person-card">
                        <div class="person-name">孙华</div>
                        <div class="person-title">财务总监</div>
                        <div class="person-role">负责财务管理、预算控制及风险审计</div>
                    </div>
                    <div class="person-card">
                        <div class="person-name">周洋</div>
                        <div class="person-title">零售管理部主任</div>
                        <div class="person-role">负责加油站网点管理、营销活动执行</div>
                    </div>
                </div>

                <h3>其他信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">实控人社会角色</div>
                        <div class="info-value">未明确</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">母公司画像</div>
                        <div class="info-value">中国石化销售股份有限公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">对口业务部门负责人</div>
                        <div class="info-value">未明确</div>
                    </div>
                </div>
            </div>

            <!-- 主管单位 -->
            <div class="card" id="supervisory-unit">
                <h2>🏛️ 主管单位</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">所属主要行业主管条线</div>
                        <div class="info-value">能源、批发零售业</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">主要主管单位</div>
                        <div class="info-value">未明确</div>
                    </div>
                </div>
            </div>

            <!-- 重点项目 -->
            <div class="card" id="key-projects">
                <h2>🚀 重点项目</h2>

                <h3>（1）行业标杆项目</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">5G+边缘计算油品调度系统</div>
                        <div class="info-value">智能化油品调度管理</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">智慧加油站AI无感支付全覆盖</div>
                        <div class="info-value">提升客户支付体验</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">易捷智能供应链管理平台</div>
                        <div class="info-value">优化供应链效率</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">充电桩智能运维平台</div>
                        <div class="info-value">新能源业务拓展</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">油库安全智能监控体系</div>
                        <div class="info-value">安全生产保障</div>
                    </div>
                </div>

                <h3>（2）近期重点项目</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>状态</th>
                                <th>预期效果</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>5G+边缘计算油品调度系统</td>
                                <td><span class="btn btn-success">已上线</span></td>
                                <td>智能化油品调度管理</td>
                            </tr>
                            <tr>
                                <td>智慧加油站AI无感支付全覆盖</td>
                                <td><span class="btn btn-success">已完成</span></td>
                                <td>提升客户支付体验</td>
                            </tr>
                            <tr>
                                <td>易捷智能供应链管理平台</td>
                                <td><span class="btn btn-primary">运营中</span></td>
                                <td>优化供应链效率</td>
                            </tr>
                            <tr>
                                <td>充电桩智能运维平台</td>
                                <td><span class="btn btn-warning">试点推广</span></td>
                                <td>新能源业务拓展</td>
                            </tr>
                            <tr>
                                <td>油库安全智能监控体系</td>
                                <td><span class="btn btn-info">建设中</span></td>
                                <td>安全生产保障</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>（3）主要DICT项目</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>合作伙伴</th>
                                <th>项目描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>5G+边缘计算油品调度系统</td>
                                <td>山东移动</td>
                                <td>智能化油品调度管理系统</td>
                            </tr>
                            <tr>
                                <td>智慧加油站AI无感支付全覆盖</td>
                                <td>支付宝/微信</td>
                                <td>AI无感支付技术应用</td>
                            </tr>
                            <tr>
                                <td>易捷智能供应链管理平台</td>
                                <td>京东科技</td>
                                <td>供应链智能化管理</td>
                            </tr>
                            <tr>
                                <td>充电桩智能运维平台</td>
                                <td>特来电</td>
                                <td>充电设施智能运维</td>
                            </tr>
                            <tr>
                                <td>油库安全智能监控体系</td>
                                <td>海康威视</td>
                                <td>安全监控智能化</td>
                            </tr>
                            <tr>
                                <td>"鸢都油+"会员生态平台</td>
                                <td>腾讯企业微信</td>
                                <td>会员生态系统建设</td>
                            </tr>
                            <tr>
                                <td>光伏电站智能监控系统</td>
                                <td>华为数字能源</td>
                                <td>光伏发电智能监控</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 政策信息 -->
        <div class="content-section" id="policy-info">
            <div class="card">
                <h2>📋 政策信息</h2>

                <h3>潍坊区域重点政策</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>文件名称</th>
                                <th>文号</th>
                                <th>发布单位</th>
                                <th>发布日期</th>
                                <th>政策类型</th>
                                <th>主要内容</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>关于建立财政金融协同联动机制全力支持乡村振兴的实施意见</td>
                                <td>未知</td>
                                <td>潍坊市财政局</td>
                                <td>2024年</td>
                                <td>政策文件</td>
                                <td>支持农业、预制菜、清洁能源等领域，提供财政贴息、基金支持等。</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>国家级政策</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>文件名称</th>
                                <th>文号</th>
                                <th>发布单位</th>
                                <th>发布日期</th>
                                <th>政策类型</th>
                                <th>主要内容</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>关于加快推进充电基础设施建设更好支持新能源汽车下乡的实施意见</td>
                                <td>国发〔2024〕15号</td>
                                <td>国家发改委、能源局</td>
                                <td>2024年5月</td>
                                <td>政策文件</td>
                                <td>鼓励国企在县域布局充电设施，简化审批流程。</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>省级政策</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>文件名称</th>
                                <th>文号</th>
                                <th>发布单位</th>
                                <th>发布日期</th>
                                <th>政策类型</th>
                                <th>主要内容</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>山东省数字强省建设规划（2023-2025年）</td>
                                <td>鲁政发〔2023〕1号</td>
                                <td>山东省政府</td>
                                <td>2023年1月</td>
                                <td>规划文件</td>
                                <td>实施"数字基建领先"工程，重点支持智慧能源、智能交通等领域。</td>
                            </tr>
                            <tr>
                                <td>山东省数字基础设施建设行动方案（2024-2025年）</td>
                                <td>鲁政办发〔2023〕2号</td>
                                <td>山东省政府</td>
                                <td>2023年2月</td>
                                <td>行动方案</td>
                                <td>对充电桩建设给予最高30%补贴，要求公共充电站100%接入省级平台。</td>
                            </tr>
                            <tr>
                                <td>山东省科技创新引领标志性产业链高质量发展实施方案（2024—2027年）</td>
                                <td>鲁政办发〔2024〕5号</td>
                                <td>山东省人民政府办公厅</td>
                                <td>2024年</td>
                                <td>发展规划</td>
                                <td>支持新一代信息技术、高端装备、新能源装备等产业链高质量发展。</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 行业热点 -->
        <div class="content-section" id="industry-trends">
            <div class="card">
                <h2>🔥 行业热点</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>热点方向</th>
                                <th>案例</th>
                                <th>效果</th>
                                <th>潜在商机</th>
                                <th>信息来源</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>新能源转型</td>
                                <td>充电桩统一接入平台</td>
                                <td>提升利用率至60%（行业平均45%）</td>
                                <td>新能源网络布局</td>
                                <td>潍坊日报（2024年12月）</td>
                            </tr>
                            <tr>
                                <td>数字化转型</td>
                                <td>智慧加油站AI无感支付全覆盖</td>
                                <td>支付效率提升40%</td>
                                <td>智能终端覆盖</td>
                                <td>山东商报（2023年12月）</td>
                            </tr>
                            <tr>
                                <td>智能监控</td>
                                <td>油库安全智能监控体系</td>
                                <td>实现烟雾/违规操作预警</td>
                                <td>安全环保技术</td>
                                <td>潍坊晚报（2023年12月）</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 招投标信息 -->
        <div class="content-section" id="bidding-info">
            <div class="card">
                <h2>📊 招投标信息</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>标讯标题</th>
                                <th>招标方式</th>
                                <th>产品</th>
                                <th>中标公司</th>
                                <th>项目地区</th>
                                <th>金额</th>
                                <th>发布日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>潍坊石油分公司昌邑第4加油站新设充电项目</td>
                                <td>竞争性谈判</td>
                                <td>充电桩</td>
                                <td>山东天业建设工程有限公司</td>
                                <td>潍坊</td>
                                <td>62.82万元</td>
                                <td>2025年5月23日</td>
                            </tr>
                            <tr>
                                <td>潍坊石油分公司潍坊油库新设充电功能项目</td>
                                <td>竞争性谈判</td>
                                <td>充电桩</td>
                                <td>山东国宏建工有限公司</td>
                                <td>潍坊</td>
                                <td>61.48万元</td>
                                <td>2024年11月27日</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 商机类型 -->
        <div class="content-section" id="business-opportunities">
            <div class="card">
                <h2>💰 商机类型</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>预算额度</th>
                                <th>启动周期</th>
                                <th>效果目标</th>
                                <th>商机可能性评分</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">充电桩智能运维平台</div>
                                    <span class="btn btn-primary" style="font-size: 10px;">显性商机</span>
                                </td>
                                <td>900万元</td>
                                <td>扩展建设期</td>
                                <td>利用率提升至60%</td>
                                <td><span class="score-badge score-medium">0.85</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">氢能加注站试点</div>
                                    <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                </td>
                                <td>集团基金支持（地方配套申报中）</td>
                                <td>规划启动期</td>
                                <td>融入"山东氢能高速"网络</td>
                                <td><span class="score-badge score-medium">0.85</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">"鸢都油+"会员生态平台</div>
                                    <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                </td>
                                <td>600万元</td>
                                <td>数据整合期</td>
                                <td>积分全国易捷门店通用</td>
                                <td><span class="score-badge score-medium">0.85</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">昌邑第4站充电项目</div>
                                    <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                </td>
                                <td>500万元</td>
                                <td>招标期</td>
                                <td>配套"司机之家"休息区</td>
                                <td><span class="score-badge score-low">0.75</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智能电网与能源互联网融合平台</div>
                                    <span class="btn btn-warning" style="font-size: 10px;">预判商机</span>
                                </td>
                                <td>未明确</td>
                                <td>未来</td>
                                <td>拓展能源互联网服务</td>
                                <td><span class="score-badge score-medium">0.85</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">氢能加注站智能化升级</div>
                                    <span class="btn btn-warning" style="font-size: 10px;">预判商机</span>
                                </td>
                                <td>未明确</td>
                                <td>未来</td>
                                <td>申请省级示范</td>
                                <td><span class="score-badge score-medium">0.80</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">数字孪生加油站管理系统</div>
                                    <span class="btn btn-warning" style="font-size: 10px;">预判商机</span>
                                </td>
                                <td>未明确</td>
                                <td>未来</td>
                                <td>AI算法优化配送</td>
                                <td><span class="score-badge score-medium">0.85</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">数字孪生油库建设</div>
                                    <span class="btn btn-info" style="font-size: 10px;">观测商机</span>
                                </td>
                                <td>未明确</td>
                                <td>未来</td>
                                <td>实现油库全要素数字化</td>
                                <td><span class="score-badge score-medium">0.80</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // 隐藏所有内容区域
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 显示选中的内容区域
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 显示或隐藏子导航
            const subNav = document.getElementById('customer-dynamics-subnav');
            if (sectionId === 'customer-dynamics') {
                subNav.style.display = 'flex';
            } else {
                subNav.style.display = 'none';
            }

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function showSubSection(subSectionId) {
            // 更新子导航状态
            const subNavItems = document.querySelectorAll('.sub-nav-item');
            subNavItems.forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 滚动到对应子章节
            const targetElement = document.getElementById(subSectionId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示战客动态的子导航
            const subNav = document.getElementById('customer-dynamics-subnav');
            subNav.style.display = 'flex';

            // 添加涟漪效果
            const buttons = document.querySelectorAll('.nav-item, .sub-nav-item, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // 添加涟漪效果的CSS
        const style = document.createElement('style');
        style.textContent = `
            .nav-item, .sub-nav-item, .btn {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
