#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理核心脚本
功能：从Word文档中提取结构化信息，生成HTML报告
"""

import os
import sys
import json
import yaml
import re
from datetime import datetime
from pathlib import Path

try:
    import docx
except ImportError:
    print("请安装python-docx: pip install python-docx")
    sys.exit(1)

class DocumentProcessor:
    def __init__(self, config_file, page_config_file):
        """初始化文档处理器"""
        self.config = self.load_config(config_file)
        self.page_config = self.load_config(page_config_file)
        self.extracted_data = {}
        self.validation_issues = []
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def extract_from_docx(self, docx_path):
        """从Word文档提取信息"""
        print(f"开始处理文档: {docx_path}")
        
        try:
            doc = docx.Document(docx_path)
            
            # 提取所有段落
            paragraphs = []
            for i, para in enumerate(doc.paragraphs):
                if para.text.strip():
                    paragraphs.append({
                        'index': i + 1,
                        'text': para.text.strip(),
                        'has_link': self.detect_links(para.text)
                    })
            
            # 提取所有表格
            tables = []
            for i, table in enumerate(doc.tables):
                table_data = []
                for j, row in enumerate(table.rows):
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip().replace('\n', ' ')
                        if cell_text:
                            row_data.append(cell_text)
                    if row_data:
                        table_data.append(row_data)
                if table_data:
                    tables.append({
                        'index': i + 1,
                        'data': table_data
                    })
            
            # 结构化提取信息
            self.extracted_data = {
                'enterprise_name': self.config['enterprise']['name'],
                'paragraphs': paragraphs,
                'tables': tables,
                'basic_info': self.extract_basic_info(paragraphs),
                'policies': self.extract_policies(paragraphs),
                'opportunities': self.extract_opportunities(paragraphs, tables),
                'projects': self.extract_projects(paragraphs, tables),
                'hot_topics': self.extract_hot_topics(paragraphs, tables),
                'bidding_info': self.extract_bidding_info(tables),
                'extraction_time': datetime.now().isoformat()
            }
            
            print(f"信息提取完成，共提取 {len(paragraphs)} 个段落，{len(tables)} 个表格")
            return self.extracted_data
            
        except Exception as e:
            print(f"文档处理失败: {e}")
            return None
    
    def detect_links(self, text):
        """检测文本中是否包含链接"""
        link_patterns = self.config.get('extraction_patterns', {}).get('policy_links', [])
        for pattern in link_patterns:
            if pattern in text:
                return True
        return False
    
    def extract_basic_info(self, paragraphs):
        """提取基础信息"""
        basic_info = {}
        
        # 查找包含基础信息的段落
        for para in paragraphs:
            text = para['text']
            
            # 提取企业名称
            if '企业名称' in text and '简称' in text:
                basic_info['name'] = text
            
            # 提取成立日期
            if '成立日期' in text:
                date_match = re.search(r'(\d{4}[-年]\d{1,2}[-月]\d{1,2})', text)
                if date_match:
                    basic_info['establishment_date'] = date_match.group(1)
            
            # 提取企业属性
            if '企业属性' in text:
                basic_info['enterprise_type'] = text
            
            # 提取经营地址
            if '经营地址' in text:
                basic_info['address'] = text
        
        return basic_info
    
    def extract_policies(self, paragraphs):
        """提取政策信息"""
        policies = []
        current_policy = {}
        
        for para in paragraphs:
            text = para['text']
            
            # 检测政策文件名称
            if '文件名称：' in text:
                if current_policy:
                    policies.append(current_policy)
                current_policy = {'name': text.replace('文件名称：', '').strip()}
            
            # 提取文号
            elif '文号：' in text:
                current_policy['number'] = text.replace('文号：', '').strip()
            
            # 提取发布单位
            elif '发布单位：' in text:
                current_policy['publisher'] = text.replace('发布单位：', '').strip()
            
            # 提取发布日期
            elif '发布日期：' in text:
                current_policy['date'] = text.replace('发布日期：', '').strip()
            
            # 提取政策类型
            elif '政策类型：' in text:
                current_policy['type'] = text.replace('政策类型：', '').strip()
            
            # 提取政策链接
            elif '政策链接：' in text:
                link = text.replace('政策链接：', '').strip()
                current_policy['link'] = link
            
            # 提取主要内容
            elif '主要内容：' in text:
                content = text.replace('主要内容：', '').strip()
                current_policy['content'] = content
        
        # 添加最后一个政策
        if current_policy:
            policies.append(current_policy)
        
        return policies
    
    def extract_opportunities(self, paragraphs, tables):
        """提取商机信息"""
        opportunities = []
        
        # 从表格中提取商机信息
        for table in tables:
            table_data = table['data']
            if len(table_data) > 0:
                headers = table_data[0]
                
                # 检查是否是商机表格
                if '商机领域' in ' '.join(headers):
                    for row in table_data[1:]:  # 跳过表头
                        if len(row) >= 5:  # 确保有足够的列
                            opportunity = {
                                'field': row[0] if len(row) > 0 else '',
                                'solution': row[1] if len(row) > 1 else '',
                                'value': row[2] if len(row) > 2 else '',
                                'difficulty': row[3] if len(row) > 3 else '',
                                'score': row[4] if len(row) > 4 else ''
                            }
                            opportunities.append(opportunity)
        
        return opportunities
    
    def extract_projects(self, paragraphs, tables):
        """提取项目信息"""
        projects = {
            'benchmark': [],
            'recent': [],
            'dict': []
        }
        
        current_section = None
        
        for para in paragraphs:
            text = para['text']
            
            # 识别项目章节
            if '行业标杆项目' in text:
                current_section = 'benchmark'
            elif '近期重点项目' in text:
                current_section = 'recent'
            elif 'DICT项目' in text:
                current_section = 'dict'
            elif current_section and '项目' in text:
                # 提取项目信息
                project_info = {'description': text}
                projects[current_section].append(project_info)
        
        return projects
    
    def extract_hot_topics(self, paragraphs, tables):
        """提取行业热点"""
        hot_topics = []
        
        # 从表格中提取行业热点
        for table in tables:
            table_data = table['data']
            if len(table_data) > 0:
                headers = table_data[0]
                
                # 检查是否是行业热点表格
                if '热点方向' in ' '.join(headers):
                    for row in table_data[1:]:  # 跳过表头
                        if len(row) >= 5:
                            topic = {
                                'direction': row[0] if len(row) > 0 else '',
                                'case': row[1] if len(row) > 1 else '',
                                'effect': row[2] if len(row) > 2 else '',
                                'opportunity': row[3] if len(row) > 3 else '',
                                'source': row[4] if len(row) > 4 else ''
                            }
                            hot_topics.append(topic)
        
        return hot_topics
    
    def extract_bidding_info(self, tables):
        """提取招投标信息"""
        bidding_info = []
        
        # 从表格中提取招投标信息
        for table in tables:
            table_data = table['data']
            if len(table_data) > 0:
                headers = table_data[0]
                
                # 检查是否是招投标表格
                if '标讯标题' in ' '.join(headers):
                    for row in table_data[1:]:  # 跳过表头
                        if len(row) >= 8:
                            bidding = {
                                'title': row[0] if len(row) > 0 else '',
                                'method': row[1] if len(row) > 1 else '',
                                'company': row[2] if len(row) > 2 else '',
                                'product': row[3] if len(row) > 3 else '',
                                'winner': row[4] if len(row) > 4 else '',
                                'region': row[5] if len(row) > 5 else '',
                                'amount': row[6] if len(row) > 6 else '',
                                'date': row[7] if len(row) > 7 else ''
                            }
                            bidding_info.append(bidding)
        
        return bidding_info
    
    def save_extracted_data(self, output_path):
        """保存提取的数据"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
            print(f"提取数据已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False

if __name__ == "__main__":
    # 测试代码
    config_file = "../configs/weifang_tobacco_config.yaml"
    page_config_file = "../configs/page_config.yaml"
    
    processor = DocumentProcessor(config_file, page_config_file)
    
    # 处理文档
    doc_path = "../input/潍坊烟草-战客动态v1.docx"
    extracted_data = processor.extract_from_docx(doc_path)
    
    if extracted_data:
        # 保存提取的数据
        output_path = "../output/extracted_data.json"
        processor.save_extracted_data(output_path)
