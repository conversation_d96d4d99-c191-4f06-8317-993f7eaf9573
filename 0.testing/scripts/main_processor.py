#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主控制脚本
功能：协调整个文档处理流程
"""

import os
import sys
import json
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from document_processor import DocumentProcessor
from validator import ContentValidator
from html_generator import HTMLGenerator

class MainProcessor:
    def __init__(self, enterprise_config, page_config):
        """初始化主处理器"""
        self.enterprise_config = enterprise_config
        self.page_config = page_config
        
        # 初始化各个组件
        self.doc_processor = DocumentProcessor(enterprise_config, page_config)
        self.validator = ContentValidator(enterprise_config)
        self.html_generator = HTMLGenerator(page_config)
        
        # 设置路径
        self.setup_paths()
    
    def setup_paths(self):
        """设置文件路径"""
        base_dir = Path(__file__).parent.parent
        
        self.paths = {
            'input_dir': base_dir / 'input',
            'output_dir': base_dir / 'output',
            'logs_dir': base_dir / 'logs',
            'configs_dir': base_dir / 'configs'
        }
        
        # 确保目录存在
        for path in self.paths.values():
            path.mkdir(exist_ok=True)
    
    def process_document(self, doc_name):
        """处理单个文档的完整流程"""
        print(f"\n{'='*60}")
        print(f"开始处理文档: {doc_name}")
        print(f"{'='*60}")
        
        # 第一步：复制输入文档
        if not self.prepare_input_document(doc_name):
            return False
        
        # 第二步：提取信息
        extracted_data = self.extract_information()
        if not extracted_data:
            return False
        
        # 第三步：验证信息
        validation_report = self.validate_information(extracted_data)
        
        # 第四步：自动修复问题
        if validation_report.get('total_issues', 0) > 0:
            extracted_data = self.auto_fix_issues(extracted_data)
        
        # 第五步：生成HTML
        if not self.generate_html_report(extracted_data):
            return False
        
        # 第六步：生成处理报告
        self.generate_process_report(validation_report)
        
        print(f"\n{'='*60}")
        print(f"文档处理完成: {doc_name}")
        print(f"{'='*60}")
        
        return True
    
    def prepare_input_document(self, doc_name):
        """准备输入文档"""
        print("\n1. 准备输入文档...")
        
        # 从配置中获取文档路径
        config_doc_path = self.doc_processor.config['enterprise']['doc_path']
        source_path = Path(__file__).parent.parent / config_doc_path
        
        # 目标路径
        target_path = self.paths['input_dir'] / doc_name
        
        try:
            if source_path.exists():
                shutil.copy2(source_path, target_path)
                print(f"✓ 文档已复制到: {target_path}")
                return True
            else:
                print(f"✗ 源文档不存在: {source_path}")
                return False
        except Exception as e:
            print(f"✗ 复制文档失败: {e}")
            return False
    
    def extract_information(self):
        """提取信息"""
        print("\n2. 提取结构化信息...")
        
        # 获取输入文档路径
        doc_name = Path(self.doc_processor.config['enterprise']['doc_path']).name
        input_path = self.paths['input_dir'] / doc_name
        
        # 提取信息
        extracted_data = self.doc_processor.extract_from_docx(str(input_path))
        
        if extracted_data:
            # 保存提取的数据
            output_path = self.paths['output_dir'] / 'extracted_data.json'
            self.doc_processor.save_extracted_data(str(output_path))
            print("✓ 信息提取完成")
            return extracted_data
        else:
            print("✗ 信息提取失败")
            return None
    
    def validate_information(self, extracted_data):
        """验证信息"""
        print("\n3. 验证信息完整性和准确性...")
        
        # 执行验证
        validation_report = self.validator.validate_extracted_data(extracted_data)
        
        # 保存验证报告
        report_path = self.paths['logs_dir'] / 'validation_report.json'
        self.validator.save_validation_report(validation_report, str(report_path))
        
        # 显示验证结果
        if validation_report['validation_passed']:
            print("✓ 验证通过")
        else:
            print(f"⚠ 发现 {validation_report['total_issues']} 个问题")
            print(f"  - 错误: {validation_report['issues_by_severity']['error']}")
            print(f"  - 警告: {validation_report['issues_by_severity']['warning']}")
        
        return validation_report
    
    def auto_fix_issues(self, extracted_data):
        """自动修复问题"""
        print("\n4. 自动修复问题...")
        
        # 执行自动修复
        fixed_data = self.validator.auto_fix_issues(extracted_data)
        
        if self.validator.fixes_applied:
            print(f"✓ 已修复 {len(self.validator.fixes_applied)} 个问题")
            for fix in self.validator.fixes_applied:
                print(f"  - {fix}")
            
            # 保存修复后的数据
            output_path = self.paths['output_dir'] / 'extracted_data_fixed.json'
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(fixed_data, f, ensure_ascii=False, indent=2)
        else:
            print("- 无需修复")
        
        return fixed_data
    
    def generate_html_report(self, extracted_data):
        """生成HTML报告"""
        print("\n5. 生成HTML报告...")
        
        # 获取输出文件名
        output_name = self.doc_processor.config['enterprise']['output_name']
        output_path = self.paths['output_dir'] / output_name
        
        # 生成HTML
        success = self.html_generator.generate_html(extracted_data, str(output_path))
        
        if success:
            print(f"✓ HTML报告已生成: {output_path}")
            return True
        else:
            print("✗ HTML生成失败")
            return False
    
    def generate_process_report(self, validation_report):
        """生成处理报告"""
        print("\n6. 生成处理报告...")
        
        # 统计信息
        enterprise_name = self.doc_processor.config['enterprise']['name']
        
        process_report = {
            'enterprise_name': enterprise_name,
            'process_time': validation_report.get('validation_time'),
            'validation_summary': {
                'total_issues': validation_report.get('total_issues', 0),
                'errors': validation_report.get('issues_by_severity', {}).get('error', 0),
                'warnings': validation_report.get('issues_by_severity', {}).get('warning', 0),
                'validation_passed': validation_report.get('validation_passed', False)
            },
            'data_statistics': self.get_data_statistics(),
            'fixes_applied': len(self.validator.fixes_applied),
            'output_files': [
                'extracted_data.json',
                'validation_report.json',
                self.doc_processor.config['enterprise']['output_name']
            ]
        }
        
        # 保存处理报告
        report_path = self.paths['logs_dir'] / 'process_report.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(process_report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 处理报告已保存: {report_path}")
        
        # 显示摘要
        self.print_process_summary(process_report)
    
    def get_data_statistics(self):
        """获取数据统计信息"""
        try:
            data_path = self.paths['output_dir'] / 'extracted_data.json'
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return {
                'paragraphs_count': len(data.get('paragraphs', [])),
                'tables_count': len(data.get('tables', [])),
                'policies_count': len(data.get('policies', [])),
                'opportunities_count': len(data.get('opportunities', [])),
                'hot_topics_count': len(data.get('hot_topics', [])),
                'bidding_info_count': len(data.get('bidding_info', []))
            }
        except Exception:
            return {}
    
    def print_process_summary(self, report):
        """打印处理摘要"""
        print(f"\n📊 处理摘要 - {report['enterprise_name']}")
        print("-" * 50)
        
        stats = report.get('data_statistics', {})
        print(f"📄 提取段落数: {stats.get('paragraphs_count', 0)}")
        print(f"📋 提取表格数: {stats.get('tables_count', 0)}")
        print(f"📜 政策信息数: {stats.get('policies_count', 0)}")
        print(f"💼 商机信息数: {stats.get('opportunities_count', 0)}")
        print(f"🔥 行业热点数: {stats.get('hot_topics_count', 0)}")
        print(f"📊 招投标信息数: {stats.get('bidding_info_count', 0)}")
        
        validation = report.get('validation_summary', {})
        print(f"\n🔍 验证结果:")
        print(f"  - 总问题数: {validation.get('total_issues', 0)}")
        print(f"  - 错误数: {validation.get('errors', 0)}")
        print(f"  - 警告数: {validation.get('warnings', 0)}")
        print(f"  - 验证通过: {'✓' if validation.get('validation_passed') else '✗'}")
        
        print(f"\n🔧 修复问题数: {report.get('fixes_applied', 0)}")
        
        print(f"\n📁 输出文件:")
        for file in report.get('output_files', []):
            print(f"  - {file}")

def main():
    """主函数"""
    print("🚀 启动文档处理系统")
    
    # 配置文件路径
    base_dir = Path(__file__).parent.parent
    enterprise_config = str(base_dir / 'configs' / 'weifang_tobacco_config.yaml')
    page_config = str(base_dir / 'configs' / 'page_config.yaml')
    
    # 创建主处理器
    processor = MainProcessor(enterprise_config, page_config)
    
    # 处理文档
    doc_name = "潍坊烟草-战客动态v1.docx"
    success = processor.process_document(doc_name)
    
    if success:
        print("\n🎉 处理完成！")
    else:
        print("\n❌ 处理失败！")
    
    return success

if __name__ == "__main__":
    main()
