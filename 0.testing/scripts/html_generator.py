#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML生成脚本
功能：基于提取的结构化数据生成HTML报告
"""

import json
import yaml
from datetime import datetime
from pathlib import Path

class HTMLGenerator:
    def __init__(self, page_config_file):
        """初始化HTML生成器"""
        self.page_config = self.load_config(page_config_file)
        self.style_config = self.page_config.get('style_config', {})
        self.content_limits = self.page_config.get('content_limits', {})
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def generate_html(self, extracted_data, output_path):
        """生成HTML报告"""
        print("开始生成HTML报告...")
        
        # 生成HTML内容
        html_content = self.build_html_structure(extracted_data)
        
        # 保存HTML文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"HTML报告已生成: {output_path}")
            return True
        except Exception as e:
            print(f"生成HTML失败: {e}")
            return False
    
    def build_html_structure(self, data):
        """构建HTML结构"""
        enterprise_name = data.get('enterprise_name', '企业')
        
        html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{enterprise_name} - 企业画像报告</title>
    {self.generate_styles()}
</head>
<body>
    <div class="container">
        {self.generate_header(enterprise_name)}
        {self.generate_navigation()}
        {self.generate_content_sections(data)}
    </div>
    {self.generate_scripts()}
</body>
</html>"""
        
        return html
    
    def generate_styles(self):
        """生成CSS样式 - PPT截图优化版"""
        page_config = self.page_config.get('page_config', {})
        style_config = self.style_config
        layout = page_config.get('layout', {})
        card_style = page_config.get('card_style', {})
        text_colors = style_config.get('text_colors', {})
        font_sizes = style_config.get('font_sizes', {})
        line_heights = style_config.get('line_heights', {})

        return f"""
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        /* 页面背景色，让白色内容卡片更突出 */
        body {{
            background-color: {layout.get('page_background', '#f0f2f5')};
            font-family: {style_config.get('font_family', "'Microsoft YaHei', Arial, sans-serif")};
            font-size: {font_sizes.get('body', '18px')};
            line-height: {line_heights.get('body', '1.8')};
            color: {text_colors.get('primary', '#333333')};
        }}

        /* 这是内容的核心容器 - 固定尺寸卡片式设计 */
        .container {{
            width: {page_config.get('fixed_width', '1200px')};
            min-height: {page_config.get('min_height', '600px')};
            margin: {layout.get('margin', '50px auto')};
            background-color: {layout.get('background_color', '#ffffff')};
            padding: {layout.get('padding', '50px')};
            box-shadow: {card_style.get('box_shadow', '0 4px 12px rgba(0, 0, 0, 0.1)')};
            border-radius: {card_style.get('border_radius', '8px')};
            box-sizing: {card_style.get('box_sizing', 'border-box')};
        }}
        
        .header {{
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, {style_config.get('primary_color', '#2c5aa0')} 0%, {style_config.get('secondary_color', '#5a7fc7')} 100%);
            color: white;
            border-radius: {card_style.get('border_radius', '8px')} {card_style.get('border_radius', '8px')} 0 0;
            margin: -{layout.get('padding', '50px').split()[0]} -{layout.get('padding', '50px').split()[0]} 30px;
        }}

        .header h1 {{
            font-size: {font_sizes.get('h1', '36px')};
            font-weight: 700;
            margin-bottom: 10px;
            line-height: {line_heights.get('heading', '1.4')};
        }}

        .header p {{
            font-size: {font_sizes.get('body', '18px')};
            opacity: 0.9;
            line-height: {line_heights.get('body', '1.8')};
        }}

        /* 文本样式优化 */
        h1, h2, h3, h4, h5, h6 {{
            color: {text_colors.get('heading', '#111111')};
            line-height: {line_heights.get('heading', '1.4')};
            margin-bottom: 15px;
        }}

        h1 {{ font-size: {font_sizes.get('h1', '36px')}; }}
        h2 {{ font-size: {font_sizes.get('h2', '28px')}; }}
        h3 {{ font-size: {font_sizes.get('h3', '24px')}; }}

        p {{
            color: {text_colors.get('secondary', '#555555')};
            margin-bottom: 15px;
            line-height: {line_heights.get('body', '1.8')};
        }}

        b, strong {{
            color: {text_colors.get('heading', '#111111')};
        }}
        
        .navigation {{
            display: flex;
            justify-content: center;
            margin-bottom: {page_config.get('section_spacing', '40px')};
            flex-wrap: wrap;
            gap: 12px;
        }}

        .nav-btn {{
            background: {style_config.get('primary_color', '#2c5aa0')};
            color: white;
            border: none;
            padding: {style_config.get('button_padding', '10px 20px')};
            border-radius: {style_config.get('button_border_radius', '6px')};
            cursor: pointer;
            font-size: {font_sizes.get('small', '14px')};
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}

        .nav-btn:hover {{
            background: {style_config.get('secondary_color', '#5a7fc7')};
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }}

        .nav-btn.active {{
            background: {style_config.get('accent_color', '#8fa4d3')};
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }}
        
        .section {{
            margin-bottom: {page_config.get('section_spacing', '40px')};
        }}

        .section-title {{
            font-size: {font_sizes.get('h2', '28px')};
            font-weight: 700;
            color: {style_config.get('primary_color', '#2c5aa0')};
            margin-bottom: 25px;
            padding-bottom: 12px;
            border-bottom: 2px solid {style_config.get('primary_color', '#2c5aa0')};
            line-height: {line_heights.get('heading', '1.4')};
        }}

        .card {{
            background: white;
            border-radius: {style_config.get('card_border_radius', '12px')};
            box-shadow: {style_config.get('card_shadow', '0 2px 8px rgba(0, 0, 0, 0.1)')};
            padding: 30px;
            margin-bottom: 25px;
            border: 1px solid #e8e8e8;
        }}

        .card-title {{
            font-size: {font_sizes.get('h3', '24px')};
            font-weight: 600;
            color: {text_colors.get('heading', '#111111')};
            margin-bottom: 20px;
            line-height: {line_heights.get('heading', '1.4')};
        }}
        
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }}
        
        .info-item {{
            display: flex;
            flex-direction: column;
        }}
        
        .info-label {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
            font-weight: 500;
        }}
        
        .info-value {{
            font-size: 1em;
            color: #1d1d1f;
            font-weight: 500;
        }}
        
        .btn {{
            display: inline-block;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 2px;
        }}
        
        .btn-primary {{ background: {style_config.get('primary_color', '#4a90a4')}; color: white; }}
        .btn-success {{ background: #5ba3b4; color: white; }}
        .btn-warning {{ background: #6b73a0; color: white; }}
        .btn-info {{ background: #7b83b0; color: white; }}
        .btn-secondary {{ background: #8b93c0; color: white; }}
        
        .table-container {{
            overflow-x: auto;
            margin: 20px 0;
        }}
        
        .table {{
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }}
        
        .table th {{
            background: {style_config.get('primary_color', '#4a90a4')};
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9em;
        }}
        
        .table td {{
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9em;
        }}
        
        .table tr:hover {{
            background: #f8f9fa;
        }}
        
        .score-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.8em;
        }}
        
        .score-high {{ background: #d4edda; color: #155724; }}
        .score-medium {{ background: #fff3cd; color: #856404; }}
        .score-low {{ background: #f8d7da; color: #721c24; }}
        
        /* 专为PPT截图优化，移除响应式设计 */
        /* 固定布局确保截图效果一致 */
    </style>"""
    
    def generate_header(self, enterprise_name):
        """生成页面头部"""
        return f"""
        <div class="header">
            <h1>{enterprise_name}</h1>
            <p>企业画像报告 · 生成时间: {datetime.now().strftime('%Y年%m月%d日')}</p>
        </div>"""
    
    def generate_navigation(self):
        """生成导航栏"""
        nav_items = [
            ("section1", "一、战客动态"),
            ("section2", "二、政策信息"),
            ("section3", "三、行业热点"),
            ("section4", "四、招投标信息"),
            ("section5", "五、商机类型")
        ]
        
        nav_html = '<div class="navigation">'
        for section_id, title in nav_items:
            nav_html += f'<button class="nav-btn" onclick="showSection(\'{section_id}\')">{title}</button>'
        nav_html += '</div>'
        
        return nav_html
    
    def generate_content_sections(self, data):
        """生成内容章节"""
        sections_html = ""
        
        # 战客动态
        sections_html += self.generate_basic_info_section(data)
        
        # 政策信息
        sections_html += self.generate_policies_section(data.get('policies', []))
        
        # 行业热点
        sections_html += self.generate_hot_topics_section(data.get('hot_topics', []))
        
        # 招投标信息
        sections_html += self.generate_bidding_section(data.get('bidding_info', []))
        
        # 商机类型
        sections_html += self.generate_opportunities_section(data.get('opportunities', []))
        
        return sections_html
    
    def generate_basic_info_section(self, data):
        """生成基础信息章节"""
        basic_info = data.get('basic_info', {})
        
        return f"""
        <div id="section1" class="section">
            <div class="section-title">一、战客动态</div>
            <div class="card">
                <div class="card-title">基础信息</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">企业名称</div>
                        <div class="info-value">{basic_info.get('name', '未知')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">成立日期</div>
                        <div class="info-value">{basic_info.get('establishment_date', '未知')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">企业属性</div>
                        <div class="info-value">{basic_info.get('enterprise_type', '未知')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">经营地址</div>
                        <div class="info-value">{basic_info.get('address', '未知')}</div>
                    </div>
                </div>
            </div>
        </div>"""
    
    def generate_policies_section(self, policies):
        """生成政策信息章节"""
        policies_html = """
        <div id="section2" class="section" style="display: none;">
            <div class="section-title">二、政策信息</div>"""
        
        for i, policy in enumerate(policies[:self.content_limits.get('max_policies', 10)]):
            link_html = ""
            if policy.get('link'):
                link_html = f"""
                    <div class="info-item">
                        <div class="info-label">政策链接</div>
                        <div class="info-value">
                            <a href="{policy['link']}" target="_blank" 
                               style="color: {self.style_config.get('primary_color', '#4a90a4')}; text-decoration: none; font-weight: 500;">
                                查看政策详情 →
                            </a>
                        </div>
                    </div>"""
            
            policies_html += f"""
            <div class="card">
                <div class="card-title">{policy.get('name', f'政策{i+1}')}</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">文号</div>
                        <div class="info-value">{policy.get('number', '未知')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布单位</div>
                        <div class="info-value">{policy.get('publisher', '未知')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">发布日期</div>
                        <div class="info-value">{policy.get('date', '未知')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">政策类型</div>
                        <div class="info-value"><span class="btn btn-info">{policy.get('type', '未知')}</span></div>
                    </div>
                    {link_html}
                </div>
                <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid {self.style_config.get('primary_color', '#4a90a4')};">
                    <strong>主要内容：</strong>{policy.get('content', '暂无描述')}
                </div>
            </div>"""
        
        policies_html += "</div>"
        return policies_html
    
    def generate_hot_topics_section(self, hot_topics):
        """生成行业热点章节"""
        if not hot_topics:
            return """
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>
                <div class="card">
                    <p>暂无行业热点信息</p>
                </div>
            </div>"""
        
        table_html = """
        <div id="section3" class="section" style="display: none;">
            <div class="section-title">三、行业热点</div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>热点方向</th>
                            <th>案例</th>
                            <th>效果</th>
                            <th>潜在商机</th>
                            <th>信息来源</th>
                        </tr>
                    </thead>
                    <tbody>"""
        
        for topic in hot_topics[:self.content_limits.get('max_hot_topics', 8)]:
            table_html += f"""
                        <tr>
                            <td><span class="btn btn-primary">{topic.get('direction', '未知')}</span></td>
                            <td>{topic.get('case', '未知')}</td>
                            <td>{topic.get('effect', '未知')}</td>
                            <td>{topic.get('opportunity', '未知')}</td>
                            <td>{topic.get('source', '未知')}</td>
                        </tr>"""
        
        table_html += """
                    </tbody>
                </table>
            </div>
        </div>"""
        
        return table_html
    
    def generate_bidding_section(self, bidding_info):
        """生成招投标信息章节"""
        if not bidding_info:
            return """
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="card">
                    <p>暂无招投标信息</p>
                </div>
            </div>"""
        
        table_html = """
        <div id="section4" class="section" style="display: none;">
            <div class="section-title">四、政采公示/招投标信息</div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>标讯标题</th>
                            <th>招标方式</th>
                            <th>招标公司</th>
                            <th>产品</th>
                            <th>中标公司</th>
                            <th>项目地区</th>
                            <th>金额</th>
                            <th>发布日期</th>
                        </tr>
                    </thead>
                    <tbody>"""
        
        for bidding in bidding_info:
            table_html += f"""
                        <tr>
                            <td>{bidding.get('title', '未知')}</td>
                            <td>{bidding.get('method', '未知')}</td>
                            <td>{bidding.get('company', '未知')}</td>
                            <td>{bidding.get('product', '未知')}</td>
                            <td>{bidding.get('winner', '未知')}</td>
                            <td>{bidding.get('region', '未知')}</td>
                            <td>{bidding.get('amount', '未知')}</td>
                            <td>{bidding.get('date', '未知')}</td>
                        </tr>"""
        
        table_html += """
                    </tbody>
                </table>
            </div>
        </div>"""
        
        return table_html
    
    def generate_opportunities_section(self, opportunities):
        """生成商机信息章节"""
        if not opportunities:
            return """
            <div id="section5" class="section" style="display: none;">
                <div class="section-title">五、商机类型</div>
                <div class="card">
                    <p>暂无商机信息</p>
                </div>
            </div>"""
        
        table_html = """
        <div id="section5" class="section" style="display: none;">
            <div class="section-title">五、商机类型</div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>商机领域</th>
                            <th>潜在解决方案</th>
                            <th>客户价值</th>
                            <th>实施难度</th>
                            <th>商机评分</th>
                        </tr>
                    </thead>
                    <tbody>"""
        
        for opportunity in opportunities[:self.content_limits.get('max_opportunities', 6)]:
            score = opportunity.get('score', '0')
            score_class = self.get_score_class(score)
            
            table_html += f"""
                        <tr>
                            <td>{opportunity.get('field', '未知')}</td>
                            <td>{opportunity.get('solution', '未知')}</td>
                            <td>{opportunity.get('value', '未知')}</td>
                            <td>{opportunity.get('difficulty', '未知')}</td>
                            <td><span class="score-badge {score_class}">{score}</span></td>
                        </tr>"""
        
        table_html += """
                    </tbody>
                </table>
            </div>
        </div>"""
        
        return table_html
    
    def get_score_class(self, score):
        """获取评分对应的CSS类"""
        try:
            score_float = float(score)
            if score_float >= 0.8:
                return "score-high"
            elif score_float >= 0.6:
                return "score-medium"
            else:
                return "score-low"
        except ValueError:
            return "score-low"
    
    def generate_scripts(self):
        """生成JavaScript脚本"""
        return """
    <script>
        function showSection(sectionId) {
            // 隐藏所有章节
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });
            
            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.nav-btn');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
            }
            
            // 添加active类到点击的按钮
            event.target.classList.add('active');
        }
        
        // 页面加载时显示第一个章节
        document.addEventListener('DOMContentLoaded', function() {
            showSection('section1');
            document.querySelector('.nav-btn').classList.add('active');
        });
    </script>"""

if __name__ == "__main__":
    # 测试代码
    page_config_file = "../configs/page_config.yaml"
    
    generator = HTMLGenerator(page_config_file)
    
    # 加载提取的数据
    with open("../output/extracted_data.json", 'r', encoding='utf-8') as f:
        extracted_data = json.load(f)
    
    # 生成HTML
    output_path = "../output/潍坊烟草-战客动态v1_Test.html"
    generator.generate_html(extracted_data, output_path)
