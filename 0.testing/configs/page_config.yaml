# 页面边界控制配置 - PPT截图优化版
page_config:
  # 固定尺寸设计（专为PPT截图优化）
  fixed_width: "1200px"        # 固定宽度，适合PPT展示
  min_height: "600px"          # 最小高度，避免内容少时截图太小

  # 页面布局控制
  layout:
    margin: "50px auto"        # 在浏览器中居中显示，方便截图
    padding: "50px"            # 充足的内边距，让内容呼吸
    background_color: "#ffffff" # 白色背景
    page_background: "#f0f2f5"  # 页面背景色，让白色内容卡片更突出

  # 卡片式设计
  card_style:
    box_shadow: "0 4px 12px rgba(0, 0, 0, 0.1)"  # 精致的阴影效果
    border_radius: "8px"       # 圆角，更美观
    box_sizing: "border-box"   # 确保padding不会撑大宽度

  # 内容区域控制
  content_padding: "30px"      # 内容区域内边距
  section_spacing: "40px"      # 章节间距

# 内容长度控制
content_limits:
  # 每个部分的最大内容量
  max_policies: 10        # 最多显示10个政策
  max_projects: 8         # 最多显示8个项目
  max_opportunities: 6    # 最多显示6个商机
  max_hot_topics: 8       # 最多显示8个行业热点
  
  # 文本长度限制
  max_description_length: 500  # 描述最多500字符
  max_title_length: 100       # 标题最多100字符
  max_content_length: 1000    # 主要内容最多1000字符

# 样式配置 - PPT截图优化版
style_config:
  # 主色调（更适合PPT展示）
  primary_color: "#2c5aa0"     # 深蓝色，更专业
  secondary_color: "#5a7fc7"   # 中蓝色
  accent_color: "#8fa4d3"      # 浅蓝色

  # 文本颜色
  text_colors:
    primary: "#333333"         # 主文本颜色
    secondary: "#555555"       # 次要文本颜色
    heading: "#111111"         # 标题颜色

  # 字体配置（更清晰的字体）
  font_family: "'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif"

  # 字体大小
  font_sizes:
    h1: "36px"                 # 主标题
    h2: "28px"                 # 二级标题
    h3: "24px"                 # 三级标题
    body: "18px"               # 正文
    small: "14px"              # 小字

  # 行高设置
  line_heights:
    heading: "1.4"             # 标题行高
    body: "1.8"                # 正文行高，提升可读性

  # 卡片样式
  card_border_radius: "12px"
  card_shadow: "0 2px 8px rgba(0, 0, 0, 0.1)"

  # 按钮样式
  button_border_radius: "6px"
  button_padding: "10px 20px"
