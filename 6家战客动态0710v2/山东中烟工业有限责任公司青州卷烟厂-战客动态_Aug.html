<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：山东中烟工业有限责任公司青州卷烟厂</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 内容区域 */
        .content {
            padding: 0 30px 30px;
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 30px 0 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 30px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subsection-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #4a90a4;
            border-radius: 2px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .info-value {
            color: #1d1d1f;
            font-size: 14px;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rating-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .rating-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .rating-stars {
            font-size: 20px;
            color: #ffd700;
        }

        .rating-details {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 按钮组 */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 卡片 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-role {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 表格 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #4a90a4;
            border-bottom: 2px solid #e9ecef;
            font-size: 14px;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 14px;
            vertical-align: top;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 评分徽章 */
        .score-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .score-high { background: #4a90a4; }
        .score-medium { background: #6b73a0; }
        .score-low { background: #8b93c0; }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">商机类型</button>
            </div>
            
            <div class="nav-secondary show">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>企业画像报告</h1>
            <div class="subtitle">山东中烟工业有限责任公司青州卷烟厂</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
