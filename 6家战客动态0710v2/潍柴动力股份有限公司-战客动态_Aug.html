<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：潍柴动力股份有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 内容区域 */
        .content {
            padding: 0 30px 30px;
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 30px 0 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 30px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subsection-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #4a90a4;
            border-radius: 2px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .info-value {
            color: #1d1d1f;
            font-size: 14px;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rating-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .rating-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .rating-stars {
            font-size: 20px;
            color: #ffd700;
        }

        .rating-details {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 按钮组 */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 卡片 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-role {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 表格 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #4a90a4;
            border-bottom: 2px solid #e9ecef;
            font-size: 14px;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 14px;
            vertical-align: top;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 评分徽章 */
        .score-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .score-high { background: #4a90a4; }
        .score-medium { background: #6b73a0; }
        .score-low { background: #8b93c0; }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">商机类型</button>
            </div>
            
            <div class="nav-secondary show">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>企业画像报告</h1>
            <div class="subtitle">潍柴动力股份有限公司</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 战客动态 -->
            <div id="section1" class="section">
                <div class="section-title">一、战客动态</div>

                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">企业名称（简称）</div>
                            <div class="info-value">潍柴动力股份有限公司（潍柴动力）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">成立日期</div>
                            <div class="info-value">2002-12-23</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">企业属性</div>
                            <div class="info-value">股份有限公司（港澳台投资、上市）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">注册资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实缴资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">经营地址</div>
                            <div class="info-value">山东省潍坊市高新区福寿东街197号甲</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">隶属关系</div>
                            <div class="info-value">山东重工集团</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要分支机构</div>
                            <div class="info-value">国内：陕西重型汽车、法士特齿轮、汉德车桥、潍柴雷沃智慧农业；境外：德国凯傲集团、林德液压、意大利法拉帝、法国博杜安</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>全球柴油机龙头，重卡发动机国内市占率38.7%，天然气重卡发动机市占率59.6%，法士特变速器市占率超80%。<br>
                                <strong>价值点：</strong>品牌影响力强，行业地位显著。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>2024年营业收入2156.91亿元，归母净利润114.03亿元，海外收入占比超35%。<br>
                                <strong>价值点：</strong>营收规模大，盈利能力较强，海外市场拓展潜力大。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>与多家国际企业有长期合作，如德国凯傲集团、林德液压等，且在多个重大项目中表现出色。<br>
                                <strong>价值点：</strong>合作稳定，履约能力强。
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">发动机、变速箱、新能源电池、叉车、智能物流设备等</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">上游制造、中游研发、下游销售和服务</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">2024年营业收入2156.91亿元</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群</span>
                        <span class="btn btn-success">规上企业</span>
                        <span class="btn btn-success">专精特新企业</span>
                        <span class="btn btn-success">省级单项冠军</span>
                    </div>
                </div>

                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">全球柴油机龙头，重卡发动机国内市占率38.7%</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">天然气重卡发动机市占率59.6%</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">法士特变速器市占率超80%</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">全国首批"卓越级智能工厂"</span>
                            <span class="btn btn-success">国家科技进步奖一等奖</span>
                            <span class="btn btn-warning">山东省科学技术进步奖一等奖</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">资质认证</div>
                            <div class="info-value">
                                <span class="btn btn-info">规上企业</span>
                                <span class="btn btn-info">专精特新企业</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专利商标统计情况</div>
                            <div class="info-value">未明确提供具体数据</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div class="person-card">
                            <div class="person-name">马常海</div>
                            <div class="person-role">董事长、法定代表人</div>
                            <div class="person-desc">负责公司战略规划</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">王德成</div>
                            <div class="person-role">总经理、首席执行官</div>
                            <div class="person-desc">负责日常经营管理</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">黄维彪</div>
                            <div class="person-role">执行董事、党委副书记</div>
                            <div class="person-desc">负责人力资源管理及党建工作</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">满慎刚</div>
                            <div class="person-role">山东重工集团党委书记、董事长</div>
                            <div class="person-desc">推动数智化转型</div>
                        </div>
                    </div>

                    <div class="card" style="margin-top: 20px;">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">实控人社会角色</div>
                                <div class="info-value">山东重工集团实际控制人，具有较强的行业影响力</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">母公司画像</div>
                                <div class="info-value">山东重工集团，是国内领先的装备制造企业</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">对口业务部门负责人</div>
                                <div class="info-value">未明确提供</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">所属主要行业主管条线</div>
                            <div class="info-value">制造业</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要主管单位</div>
                            <div class="info-value">山东省工业和信息化厅、潍坊市科技局等</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">高端发动机数字化工厂</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>项目描述：</strong>入选"卓越级智能工厂"，实现高端发动机的数字化生产。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>投资规模</th>
                                        <th>状态</th>
                                        <th>预期效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>潍柴弗迪新能源产业园</td>
                                        <td>总投资560亿元</td>
                                        <td><span class="btn btn-success">一期2024年3月投产</span></td>
                                        <td>一期20GWh产能投产</td>
                                    </tr>
                                    <tr>
                                        <td>氢燃料重卡示范运营</td>
                                        <td>-</td>
                                        <td><span class="btn btn-info">运营中</span></td>
                                        <td>累计超100万公里</td>
                                    </tr>
                                    <tr>
                                        <td>矿山服务2.0数字化平台</td>
                                        <td>-</td>
                                        <td><span class="btn btn-success">已建成</span></td>
                                        <td>提升运营效率30%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>技术方案</th>
                                        <th>应用场景</th>
                                        <th>效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>高端发动机数字化工厂</td>
                                        <td>智能制造+工业互联网</td>
                                        <td>发动机生产制造</td>
                                        <td>加工自动化率100%</td>
                                    </tr>
                                    <tr>
                                        <td>矿山服务2.0数字化平台</td>
                                        <td>大数据+云计算</td>
                                        <td>矿山设备运营管理</td>
                                        <td>提升运营效率30%</td>
                                    </tr>
                                    <tr>
                                        <td>智能物流系统</td>
                                        <td>物联网+AI调度</td>
                                        <td>仓储物流管理</td>
                                        <td>物流效率显著提升</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 政策信息 -->
            <div id="section2" class="section" style="display: none;">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">关于印发《关于加快高新技术企业发展的实施意见》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市科学技术局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年9月12日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">高新技术企业发展支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>支持高新技术企业培育，强化科技创新能力，推动产业转型升级。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">国家级政策</div>

                    <div class="card">
                        <div class="card-title">《"十四五"智能制造发展规划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">工信部等八部门联合发布</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工业和信息化部</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2021年12月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-primary">智能制造发展</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>引导智能工厂梯度培育，支持企业数字化转型。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">《山东省集中式风电光伏发电项目竞争性配置工作管理办法（试行）》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省发展和改革委员会、山东省能源局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-success">新能源项目开发</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>主要内容：</strong>规范风电、光伏项目竞争性配置，推动新能源产业发展。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">《山东省科技创新引领标志性产业链高质量发展实施方案（2024—2027年）》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府办公厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-warning">科技创新与产业升级</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>主要内容：</strong>推动科技创新引领标志性产业链高质量发展，重点支持高端装备、新能源等领域。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行业热点 -->
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>热点方向</th>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>潜在商机</th>
                                    <th>信息来源</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="btn btn-primary">智能制造</span></td>
                                    <td>高端发动机数字化工厂，加工自动化率100%</td>
                                    <td>生产效率提升，成本降低，市场竞争力增强</td>
                                    <td>智能制造工厂升级</td>
                                    <td>潍柴动力2024年年报</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-success">新能源</span></td>
                                    <td>潍柴弗迪新能源产业园，一期20GWh产能投产</td>
                                    <td>新能源电池产能大幅提升</td>
                                    <td>新能源电池AI管理平台</td>
                                    <td>潍柴动力2024年年报</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-warning">绿色低碳</span></td>
                                    <td>氢燃料重卡示范运营，累计超100万公里</td>
                                    <td>推动绿色交通发展</td>
                                    <td>氢能源相关技术服务</td>
                                    <td>中国机械工业联合会报告</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-info">数字化转型</span></td>
                                    <td>矿山服务2.0数字化平台，提升运营效率30%</td>
                                    <td>数字化管理水平显著提升</td>
                                    <td>5G+智慧矿山解决方案</td>
                                    <td>潍柴动力2024年年报</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 招投标信息 -->
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="card" style="text-align: center; padding: 60px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                        <div style="font-size: 48px; color: #6c757d; margin-bottom: 16px;">📋</div>
                        <div style="font-size: 18px; color: #6c757d; font-weight: 500;">暂无相关信息</div>
                        <div style="font-size: 14px; color: #adb5bd; margin-top: 8px;">当前没有可显示的政采公示或招投标信息</div>
                    </div>
                </div>
            </div>

            <!-- 商机类型 -->
            <div id="section5" class="section" style="display: none;">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机领域</th>
                                    <th>潜在解决方案</th>
                                    <th>客户价值</th>
                                    <th>实施难度</th>
                                    <th>商机评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智能制造工厂升级</div>
                                        <span class="btn btn-primary" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>5G+工业互联网平台（设备互联+AI工艺优化）</td>
                                    <td>提升自动化率10%，降低故障停机20%</td>
                                    <td>中</td>
                                    <td><span class="score-badge score-high">0.85</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智慧物流系统优化</div>
                                        <span class="btn btn-warning" style="font-size: 10px;">预判商机</span>
                                    </td>
                                    <td>定制化云端调度算法+AGV协同控制系统</td>
                                    <td>仓储效率提升25%，人力成本降15%</td>
                                    <td>高</td>
                                    <td><span class="score-badge score-medium">0.75</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">5G+智慧矿山解决方案</div>
                                        <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                    </td>
                                    <td>矿区5G专网+边缘计算+车路协同平台</td>
                                    <td>实现无人运输闭环，安全性提升30%</td>
                                    <td>高</td>
                                    <td><span class="score-badge score-medium">0.80</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">新能源电池AI管理平台</div>
                                        <span class="btn btn-primary" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>电池热管理云平台+充放电数据安全监控</td>
                                    <td>延长电池寿命20%，故障预警响应速度提升50%</td>
                                    <td>中</td>
                                    <td><span class="score-badge score-high">0.90</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">农业装备物联网平台</div>
                                        <span class="btn btn-info" style="font-size: 10px;">观测商机</span>
                                    </td>
                                    <td>农机远程诊断系统+作业大数据分析</td>
                                    <td>减少售后响应时间40%，优化农机调度效率</td>
                                    <td>低</td>
                                    <td><span class="score-badge score-low">0.70</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });

            // 显示选中的section
            document.getElementById(sectionId).style.display = 'block';

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');

            // 控制子导航显示
            const subNav = document.querySelector('.nav-secondary');
            if (sectionId === 'section1') {
                subNav.classList.add('show');
            } else {
                subNav.classList.remove('show');
            }

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示第一个section
            document.getElementById('section1').style.display = 'block';

            // 添加涟漪效果
            const buttons = document.querySelectorAll('.nav-item, .nav-sub-item, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // 添加涟漪效果的CSS
        const style = document.createElement('style');
        style.textContent = `
            .nav-item, .nav-sub-item, .btn {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
