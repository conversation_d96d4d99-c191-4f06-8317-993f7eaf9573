<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：弘润石化（潍坊）有限责任公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 内容区域 */
        .content {
            padding: 0 30px 30px;
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 30px 0 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 30px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subsection-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #4a90a4;
            border-radius: 2px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #4a90a4;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .info-value {
            color: #1d1d1f;
            font-size: 14px;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .rating-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .rating-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .rating-stars {
            font-size: 20px;
            color: #ffd700;
        }

        .rating-details {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 按钮组 */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 卡片 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(74, 144, 164, 0.3);
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-role {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 表格 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #4a90a4;
            border-bottom: 2px solid #e9ecef;
            font-size: 14px;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 14px;
            vertical-align: top;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 评分徽章 */
        .score-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            color: white;
        }

        .score-high { background: #4a90a4; }
        .score-medium { background: #6b73a0; }
        .score-low { background: #8b93c0; }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">商机类型</button>
            </div>
            
            <div class="nav-secondary show">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>企业画像报告</h1>
            <div class="subtitle">弘润石化（潍坊）有限责任公司</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 战客动态 -->
            <div id="section1" class="section">
                <div class="section-title">一、战客动态</div>

                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">企业名称（简称）</div>
                            <div class="info-value">弘润石化（潍坊）有限责任公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">成立日期</div>
                            <div class="info-value">1997年01月23日</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">企业属性</div>
                            <div class="info-value">有限责任公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">注册资金</div>
                            <div class="info-value">79,358万元</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实缴资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">经营地址</div>
                            <div class="info-value">潍坊高新技术产业开发区福寿东街中段</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">隶属关系</div>
                            <div class="info-value">无明确隶属关系，由董鹏实际控制</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要分支机构</div>
                            <div class="info-value">无明确提及</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>2024年山东民企百强第6位，潍坊第一大民企（营收超900亿元），化工产业"链主企业"，带动潍坊工业营收占比33.3%。<br>
                                <strong>价值点：</strong>行业地位高，市场影响力强，品牌知名度高。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>作为大型石化企业，具备较强的盈利能力与市场拓展潜力。<br>
                                <strong>价值点：</strong>营收规模庞大，具备良好的合作基础和扩展空间。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>未提供具体合作记录，但作为行业龙头，具备较高的履约能力和合作意愿。<br>
                                <strong>价值点：</strong>具备较强的合作能力和履约保障。
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">柴油汽油、沥青、石油焦、丙烯丙烷、二甲苯；高端聚丙烯新材料、III类润滑油、食品级白油等</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">炼化综合能力800万吨/年，储油能力1270万方（全国最大保税库），高端聚丙烯产能45万吨/年（亚洲最大智慧化无人车间）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">2024年总额1395.28亿元（位列中国民企500强第61位）</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群</span>
                        <span class="btn btn-success">规上企业</span>
                        <span class="btn btn-success">专精特新企业</span>
                        <span class="btn btn-success">省级单项冠军</span>
                    </div>
                </div>

                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">2024年山东民企百强第6位，潍坊第一大民企（营收超900亿元），化工产业"链主企业"</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">炼化综合能力800万吨/年，储油能力1270万方（全国最大保税库），高端聚丙烯产能45万吨/年</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">拥有261项专利，突破III类润滑油、食品级白油等"卡脖子"技术，间二甲苯全国第一</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">2024年山东民营企业200强（第6位）</span>
                            <span class="btn btn-success">国家绿色工厂</span>
                            <span class="btn btn-warning">潍坊市"40年·40人"杰出人物（创始人董华友）</span>
                            <span class="btn btn-info">全国劳动模范拟表彰对象（董事长韩红亮）</span>
                            <span class="btn btn-secondary">中国石油化工百强企业</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">资质认证</div>
                            <div class="info-value">
                                <span class="btn btn-info">规上企业</span>
                                <span class="btn btn-info">专精特新企业</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专利商标统计情况</div>
                            <div class="info-value">
                                专利总数：<strong>261项</strong>（截至2025年6月）<br>
                                核心技术领域：炼化工艺优化、新材料合成、环保技术
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div class="person-card">
                            <div class="person-name">董鹏</div>
                            <div class="person-role">董事长、法定代表人</div>
                            <div class="person-desc">主导集团整体运营</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">臧法收</div>
                            <div class="person-role">总经理、董事</div>
                            <div class="person-desc">主管生产与项目落地</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">李洪斗</div>
                            <div class="person-role">董事</div>
                            <div class="person-desc">参与重大业务布局</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">史新村</div>
                            <div class="person-role">董事</div>
                            <div class="person-desc">参与公司运营决策</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">仲伟华</div>
                            <div class="person-role">董事</div>
                            <div class="person-desc">主管技术领域</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">毕鹏良</div>
                            <div class="person-role">监事</div>
                            <div class="person-desc">负责财务与合规监督</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">张成磊</div>
                            <div class="person-role">监事（子公司）</div>
                            <div class="person-desc">主管安全生产与质量管理</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">韩红亮</div>
                            <div class="person-role">子公司董事长</div>
                            <div class="person-desc">负责科技板块业务</div>
                        </div>
                    </div>

                    <div class="card" style="margin-top: 20px;">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">实控人社会角色</div>
                                <div class="info-value">董鹏为实控人，其父董华友为创始人，已故</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">母公司画像</div>
                                <div class="info-value">弘润资管、弘润控股等多家子公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">对口业务部门负责人</div>
                                <div class="info-value">未提供具体对接人信息</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">所属主要行业主管条线</div>
                            <div class="info-value">石油、煤炭及其他燃料加工业</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要主管单位</div>
                            <div class="info-value">未明确提及</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">高端聚丙烯智慧化无人车间</span>
                            <span class="btn btn-info">聚酰亚胺全产业链数字化升级</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>状态</th>
                                        <th>预期效果</th>
                                        <th>合作方</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>锅炉智能优化控制系统</td>
                                        <td><span class="btn btn-success">已投运</span></td>
                                        <td>提升能效，降低排放</td>
                                        <td>和利时集团</td>
                                    </tr>
                                    <tr>
                                        <td>安全生产信息化平台</td>
                                        <td><span class="btn btn-success">验收运营</span></td>
                                        <td>提升安全管理水平</td>
                                        <td>自研</td>
                                    </tr>
                                    <tr>
                                        <td>聚丙烯智慧化无人车间</td>
                                        <td><span class="btn btn-success">已投产</span></td>
                                        <td>实现无人化生产</td>
                                        <td>自研+AI技术集成</td>
                                    </tr>
                                    <tr>
                                        <td>设备预测性维护系统</td>
                                        <td><span class="btn btn-warning">实施中</span></td>
                                        <td>降低设备故障率</td>
                                        <td>华为+自研</td>
                                    </tr>
                                    <tr>
                                        <td>AI视频监控与环保中控系统</td>
                                        <td><span class="btn btn-warning">升级中</span></td>
                                        <td>智能监控与环保管理</td>
                                        <td>华为+联通+云鼎科技</td>
                                    </tr>
                                    <tr>
                                        <td>智能调度中心</td>
                                        <td><span class="btn btn-info">规划中</span></td>
                                        <td>优化生产调度</td>
                                        <td>华为联合开发</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>技术方案</th>
                                        <th>应用场景</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>安全生产信息化平台</td>
                                        <td>物联网+大数据分析</td>
                                        <td>安全监控与预警</td>
                                    </tr>
                                    <tr>
                                        <td>锅炉智能优化控制系统</td>
                                        <td>AI算法+自动控制</td>
                                        <td>能效优化与排放控制</td>
                                    </tr>
                                    <tr>
                                        <td>聚丙烯智慧化无人车间</td>
                                        <td>工业机器人+AI视觉</td>
                                        <td>无人化生产线</td>
                                    </tr>
                                    <tr>
                                        <td>设备预测性维护系统</td>
                                        <td>传感器+机器学习</td>
                                        <td>设备健康管理</td>
                                    </tr>
                                    <tr>
                                        <td>AI视频监控与环保中控系统</td>
                                        <td>AI视频分析+环保监测</td>
                                        <td>智能监控与环保管理</td>
                                    </tr>
                                    <tr>
                                        <td>智能调度中心</td>
                                        <td>大数据+智能算法</td>
                                        <td>生产调度优化</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 政策信息 -->
            <div id="section2" class="section" style="display: none;">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">关于印发《关于加快高新技术企业发展的实施意见》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市科学技术局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年9月12日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">高新技术企业发展支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>鼓励高新技术企业发展，提供税收优惠、审批绿色通道等支持措施。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">关于开展第三批省级产教融合型企业建设培育工作的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">济宁市发展和改革委员会</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-success">产教融合支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>主要内容：</strong>推动产教融合型企业建设，提供税收优惠、用地保障等支持。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">关于印发《山东省集中式风电光伏发电项目竞争性配置工作管理办法（试行）》的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省发展和改革委员会、山东省能源局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-warning">新能源项目支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>主要内容：</strong>推动风电、光伏项目竞争性配置，促进清洁能源发展。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">山东省人民政府办公厅关于印发科技创新引领标志性产业链高质量发展实施方案（2024—2027年）的通知</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府办公厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">未公开</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">科技创新引领产业链发展</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>推动科技创新引领标志性产业链高质量发展，支持高端装备制造、人工智能等领域。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行业热点 -->
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>热点方向</th>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>潜在商机</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="btn btn-primary">数字化转型</span></td>
                                    <td>聚丙烯智慧化无人车间</td>
                                    <td>提升生产效率，降低运维成本</td>
                                    <td>推动更多智能化改造项目</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-success">绿色低碳</span></td>
                                    <td>灵活焦化工艺减碳</td>
                                    <td>降低碳排放，提升环保水平</td>
                                    <td>推动绿色技术应用</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-warning">新材料研发</span></td>
                                    <td>聚酰亚胺全产业链</td>
                                    <td>提升产品附加值，增强市场竞争力</td>
                                    <td>扩展新材料市场</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-info">智能制造</span></td>
                                    <td>AI视频监控与环保中控系统</td>
                                    <td>提升安全与环保管理水平</td>
                                    <td>推动智能制造应用</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 招投标信息 -->
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="card" style="text-align: center; padding: 60px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                        <div style="font-size: 48px; color: #6c757d; margin-bottom: 16px;">📋</div>
                        <div style="font-size: 18px; color: #6c757d; font-weight: 500;">暂无相关信息</div>
                        <div style="font-size: 14px; color: #adb5bd; margin-top: 8px;">当前没有可显示的政采公示或招投标信息</div>
                    </div>
                </div>
            </div>

            <!-- 商机类型 -->
            <div id="section5" class="section" style="display: none;">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机领域</th>
                                    <th>潜在解决方案</th>
                                    <th>客户价值</th>
                                    <th>实施难度</th>
                                    <th>商机评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智能工厂</div>
                                        <span class="btn btn-primary" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>5G专网+工业物联网平台+边缘计算</td>
                                    <td>实现无人车间全流程监控，故障停机减少40%，年增效超2亿元</td>
                                    <td>中（需兼容埃克森美孚设备协议）</td>
                                    <td><span class="score-badge score-high">0.93</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智慧安防监测</div>
                                        <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                    </td>
                                    <td>AI视频分析+物联网传感器+大数据预警</td>
                                    <td>储罐泄漏响应时间缩至5分钟内，年规避安全损失≥5000万</td>
                                    <td>低（现有基础设施完善）</td>
                                    <td><span class="score-badge score-medium">0.88</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">绿色低碳云平台</div>
                                        <span class="btn btn-success" style="font-size: 10px;">准显性商机</span>
                                    </td>
                                    <td>智慧能源管理+碳足迹SaaS</td>
                                    <td>满足ESG监管，年减碳10万吨，获政府补贴3000万+</td>
                                    <td>低（政策驱动需求明确）</td>
                                    <td><span class="score-badge score-high">0.90</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">高性能研发云</div>
                                        <span class="btn btn-warning" style="font-size: 10px;">预判商机</span>
                                    </td>
                                    <td>移动云HPC+研发协同平台</td>
                                    <td>新材料研发周期缩短60%，加速6项实验室成果转化（军事/AI领域）</td>
                                    <td>高（需定制化算法）</td>
                                    <td><span class="score-badge score-medium">0.85</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">区块链供应链金融</div>
                                        <span class="btn btn-info" style="font-size: 10px;">观测商机</span>
                                    </td>
                                    <td>区块链贸易存证+跨境支付安全系统</td>
                                    <td>提升交割库交易可信度，年降低融资成本2000万</td>
                                    <td>中（需对接上海期交所）</td>
                                    <td><span class="score-badge score-medium">0.82</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });

            // 显示选中的section
            document.getElementById(sectionId).style.display = 'block';

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');

            // 控制子导航显示
            const subNav = document.querySelector('.nav-secondary');
            if (sectionId === 'section1') {
                subNav.classList.add('show');
            } else {
                subNav.classList.remove('show');
            }

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示第一个section
            document.getElementById('section1').style.display = 'block';

            // 添加涟漪效果
            const buttons = document.querySelectorAll('.nav-item, .nav-sub-item, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // 添加涟漪效果的CSS
        const style = document.createElement('style');
        style.textContent = `
            .nav-item, .nav-sub-item, .btn {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
