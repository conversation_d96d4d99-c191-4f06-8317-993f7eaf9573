<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：潍坊市中医院</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            opacity: 0;
            animation: fadeInUp 0.8s ease forwards;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
            opacity: 0;
            animation: fadeInUp 0.8s ease 0.2s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 内容区域 */
        .content {
            padding: 30px;
        }

        .section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 40px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            padding-left: 12px;
            border-left: 4px solid #4a90a4;
        }

        /* 卡片系统 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #1d1d1f;
            font-weight: 600;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4, #6b73a0);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .rating-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .rating-stars {
            color: #ffd700;
            font-size: 18px;
            margin-left: 10px;
        }

        .rating-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rating-details {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .person-title, .person-role {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .person-desc {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.85;
        }

        /* 按钮系统 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-group {
            margin: 15px 0;
        }

        /* 表格系统 */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }

        .table th {
            background: #f8f9fa;
            color: #1d1d1f;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            border-bottom: 1px solid #e9ecef;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 13px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">一、战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">二、政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">三、行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">四、招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">五、商机类型</button>
            </div>
            
            <div class="nav-secondary" id="nav-secondary">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>潍坊市中医院</h1>
            <p>企业画像报告</p>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 一、战客动态 -->
            <div id="section1" class="section active">
                <div class="section-title">一、战客动态</div>

                <!-- 1. 基础信息 -->
                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    
                    <div class="card">
                        <div class="card-title">企业基本信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">企业名称</div>
                                <div class="info-value">潍坊市中医院</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成立日期</div>
                                <div class="info-value">1955年3月（建院时原名"昌潍专区中医门诊部"）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">企业属性</div>
                                <div class="info-value">事业单位</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">注册资金</div>
                                <div class="info-value">无</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">实缴资金</div>
                                <div class="info-value">无</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">经营地址</div>
                                <div class="info-value">山东省潍坊市奎文区潍州路1055号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">隶属关系</div>
                                <div class="info-value">潍坊市卫生健康委员会主管</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">主要分支机构</div>
                                <div class="info-value">高新院区（中医医疗中心）、东院区（奎文区）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 价值度评级 -->
                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>医院为国家中医药传承创新工程重点中医医院建设单位，全国中医医院竞争力地市级医院第9名，拥有国家级重点专科3个。<br>
                                <strong>价值点：</strong>具有较强的品牌影响力和区域认可度，是潍坊市中医药服务的重要窗口。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>已与中国移动潍坊公司开展多个项目合作，如5G+智慧医院战略合作（金额1,480万元），未来在物联网、云计算、远程会诊等领域仍有较大拓展空间。<br>
                                <strong>价值点：</strong>当前合作规模稳定，且具备持续增长潜力，尤其在智慧医院、数字化转型方向。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度评级
                                <span class="rating-stars">★★★★☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>与中国移动已有多个项目落地，包括5G专网部署、智慧中药房系统等，合作关系较为稳固。<br>
                                <strong>价值点：</strong>履约能力强，合作流程规范，具备较高的信任基础，有利于后续深化合作。
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3. 经营画像 -->
                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">国家级重点专科：脑病科、心血管科、康复科；院内制剂：129种注册制剂，其中45种纳入省医保；智慧中药房：日煎煮量达4,000贴，覆盖全市12县市区。</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">区域性三级甲等中医医院，承担潍坊市及周边地区的中医诊疗、科研、教学任务，是本地中医药服务体系的核心力量。</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">未公开</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群（潍坊市中医药产业体系重要组成部分）</span>
                        <span class="btn btn-secondary">规上企业（否，事业单位，非企业编制）</span>
                        <span class="btn btn-secondary">专精特新企业（否，事业单位）</span>
                        <span class="btn btn-secondary">省级单项冠军（否）</span>
                    </div>
                </div>

                <!-- 4. 行业画像 -->
                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">全国中医医院竞争力地市级排名第9</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">电子病历六级评审通过（全国仅19家中医医院达标）</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">互联互通四级甲等认证（全国中医医院仅12家通过）</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">山东省卫生健康工作先进集体（2024年）</span>
                            <span class="btn btn-success">全国中医医院竞争力地市级医院第9名（艾力彼医院管理中心2024年发布）</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">资质认证</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">资质认证</div>
                                <div class="info-value">无明确"专精特新"或"高新技术企业"认定，但具备多项国家级、省级医疗技术资质</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">专利/商标统计情况</div>
                                <div class="info-value">未公开</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 5. 关键人 -->
                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div class="person-card">
                            <div class="person-name">肖洪涛</div>
                            <div class="person-title">党委书记</div>
                            <div class="person-desc">主持党委全面工作。联系方式：未公开</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">王辉</div>
                            <div class="person-title">党委副书记、院长</div>
                            <div class="person-desc">主持行政全面工作。联系方式：未公开</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">王学功</div>
                            <div class="person-title">副院长</div>
                            <div class="person-desc">分管后勤、信息、安全。联系方式：未公开</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">张福英</div>
                            <div class="person-title">副院长</div>
                            <div class="person-desc">分管护理、医保、药事。联系方式：未公开</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">刘兴山</div>
                            <div class="person-title">副院长</div>
                            <div class="person-desc">分管设备、采购、门诊。联系方式：未公开</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">李军</div>
                            <div class="person-title">纪委书记</div>
                            <div class="person-desc">负责纪检、监察。联系方式：未公开</div>
                        </div>
                    </div>

                    <div class="card" style="margin-top: 20px;">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">实控人社会角色</div>
                                <div class="info-value">王辉院长为中华中医药学会理事、山东省中医药学会常务理事、潍坊市政协委员</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">母公司画像</div>
                                <div class="info-value">无母公司，直属潍坊市卫健委</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">对口业务部门负责人及对接人信息</div>
                                <div class="info-value">未公开</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 6. 主管单位 -->
                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>

                    <div class="card">
                        <div class="card-title">主要主管单位</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">所属主要行业主管条线</div>
                                <div class="info-value">医疗卫生行业</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">主要主管单位</div>
                                <div class="info-value">潍坊市卫生健康委员会</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 7. 重点项目 -->
                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">国家中医特色重点医院建设项目（国家级，总投资1.8亿元）</span>
                            <span class="btn btn-success">高新院区（中医医疗中心）建设（省级重点项目）</span>
                            <span class="btn btn-warning">5G+智慧医院战略合作（中国移动合作项目）</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="subsection-title" style="font-size: 16px; margin: 20px 0 15px 0;">已立项项目的推进阶段：</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>推进阶段</th>
                                        <th>项目内容</th>
                                        <th>配套需求</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>高新院区建设</td>
                                        <td>主体施工阶段</td>
                                        <td>中医医疗中心建设</td>
                                        <td>信息化系统配套</td>
                                    </tr>
                                    <tr>
                                        <td>5G+智慧医院</td>
                                        <td>专网部署完成</td>
                                        <td>5G专网及智慧医院系统</td>
                                        <td>应用系统集成</td>
                                    </tr>
                                    <tr>
                                        <td>物联网全院覆盖工程</td>
                                        <td>分期实施中</td>
                                        <td>全院物联网基础平台建设</td>
                                        <td>设备接入与数据集成</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="subsection-title" style="font-size: 16px; margin: 20px 0 15px 0;">待开发项目的商机价值评级：</div>
                        <div class="btn-group">
                            <span class="btn btn-warning">电子病历六级升七级工程（金额860万元）</span>
                            <span class="btn btn-warning">医疗废物智能追溯管理系统</span>
                            <span class="btn btn-warning">DRG智能审核系统</span>
                            <span class="btn btn-warning">模块化数据中心建设（高新院区）</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>项目金额</th>
                                        <th>应用场景</th>
                                        <th>效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>5G+中医远程会诊平台</td>
                                        <td>680万元</td>
                                        <td>远程会诊、医联体协作</td>
                                        <td>实现21家医联体的4K高清视频会诊</td>
                                    </tr>
                                    <tr>
                                        <td>智慧中药房系统</td>
                                        <td>1,200万元</td>
                                        <td>中药煎煮、配送管理</td>
                                        <td>日煎煮量达4,000贴，覆盖全市12县市区</td>
                                    </tr>
                                    <tr>
                                        <td>全院物联网覆盖工程</td>
                                        <td>920万元</td>
                                        <td>设备监控、环境管理</td>
                                        <td>实现全院设备智能化管理</td>
                                    </tr>
                                    <tr>
                                        <td>电子病历系统升级至七级</td>
                                        <td>860万元</td>
                                        <td>病历管理、临床决策支持</td>
                                        <td>提升临床信息化水平</td>
                                    </tr>
                                    <tr>
                                        <td>模块化数据中心建设（高新院区）</td>
                                        <td>1,520万元</td>
                                        <td>数据存储、计算支撑</td>
                                        <td>支撑高新院区信息化建设</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 二、政策信息 -->
            <div id="section2" class="section">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">《潍坊市新型智慧城市发展规划（2025-2027）》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">未提供</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市政府</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2025年1月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-primary">区域发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>建设市级健康医疗大数据平台；推动中医治未病智能服务平台建设；实现三甲医院互联互通四级甲等达标率100%。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">国家级政策</div>

                    <div class="card">
                        <div class="card-title">《"十四五"中医药信息化发展规划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">国中医药规财发〔2024〕1号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">国家中医药管理局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年1月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-secondary">行业发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b93c0;">
                            <strong>主要内容：</strong>到2025年，三级中医医院电子病历应用水平全部达到5级；建设10个国家中医智能医疗示范中心；潍坊市中医院目标于2024年12月通过电子病历六级评审。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">《山东省"互联网+医疗健康"示范省建设行动计划（2025）》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政办发〔2025〕2号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省政府办公厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2025年2月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">专项行动计划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #7b83b0;">
                            <strong>主要内容：</strong>到2025年，智慧服务三级医院占比超40%；推广中医人工智能辅助决策系统；潍坊市中医院已于2024年10月通过智慧服务三级评审。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 三、行业热点 -->
            <div id="section3" class="section">
                <div class="section-title">三、行业热点</div>

                <div class="subsection">
                    <div class="card">
                        <div class="card-title">热点方向</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">5G+远程医疗</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">智慧中药房建设</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">电子病历系统升级</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #8b5a9c;">物联网在医院管理中的应用</li>
                        </ul>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>信息来源</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>5G+中医远程会诊平台</td>
                                    <td>实现21家医联体的4K高清视频会诊，提升区域协同诊疗能力</td>
                                    <td>《潍坊市中医院5G+智慧医院建设进展》（时间：2025年3月）</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="card">
                        <div class="card-title">潜在商机</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">可复制推广至其他县域医疗机构，推动5G+医疗联合体建设</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 四、招投标信息 -->
            <div id="section4" class="section">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>标讯标题</th>
                                    <th>招标方式</th>
                                    <th>招标公司</th>
                                    <th>产品</th>
                                    <th>中标公司</th>
                                    <th>项目地区</th>
                                    <th>金额</th>
                                    <th>发布日期</th>
                                    <th>链接</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>电子病历系统六级升七级工程</td>
                                    <td>公开招标</td>
                                    <td>潍坊市公共资源交易中心</td>
                                    <td>医院信息系统升级</td>
                                    <td>卫宁健康科技集团</td>
                                    <td>山东潍坊</td>
                                    <td>860万元</td>
                                    <td>2024年11月5日</td>
                                    <td>未知</td>
                                </tr>
                                <tr>
                                    <td>智慧中药房区域配送中心建设</td>
                                    <td>公开招标</td>
                                    <td>潍坊市公共资源交易中心</td>
                                    <td>智慧中药房系统</td>
                                    <td>东华医为科技股份有限公司</td>
                                    <td>山东潍坊</td>
                                    <td>1,200万元</td>
                                    <td>2024年8月12日</td>
                                    <td>未知</td>
                                </tr>
                                <tr>
                                    <td>5G+中医远程会诊平台</td>
                                    <td>公开招标</td>
                                    <td>潍坊市公共资源交易中心</td>
                                    <td>远程会诊系统</td>
                                    <td>中国移动通信集团潍坊分公司</td>
                                    <td>山东潍坊</td>
                                    <td>680万元</td>
                                    <td>2024年9月3日</td>
                                    <td>未知</td>
                                </tr>
                                <tr>
                                    <td>全院物联网（IoT）基础平台</td>
                                    <td>公开招标</td>
                                    <td>潍坊市公共资源交易中心</td>
                                    <td>物联网系统</td>
                                    <td>中国电信潍坊分公司</td>
                                    <td>山东潍坊</td>
                                    <td>920万元</td>
                                    <td>2025年3月18日</td>
                                    <td>未知</td>
                                </tr>
                                <tr>
                                    <td>高新院区模块化数据中心</td>
                                    <td>公开招标</td>
                                    <td>潍坊市公共资源交易中心</td>
                                    <td>数据中心建设</td>
                                    <td>浪潮电子信息产业股份有限公司</td>
                                    <td>山东潍坊</td>
                                    <td>1,520万元</td>
                                    <td>2025年5月9日</td>
                                    <td>未知</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 五、商机类型 -->
            <div id="section5" class="section">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机编号</th>
                                    <th>场景/方案</th>
                                    <th>移动可交付内容</th>
                                    <th>客户匹配度</th>
                                    <th>竞争格局</th>
                                    <th>落地评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>名医堂 5G 专网+4K 远程会诊</td>
                                    <td>5G 尊享专网+云视讯+边缘云</td>
                                    <td>刚需高清低延迟</td>
                                    <td>电信/联通跟进</td>
                                    <td><span class="btn btn-success">0.90</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>智慧共享中药房 5G+AI 质检</td>
                                    <td>OnePower 工业视觉+5G AGV</td>
                                    <td>产能与质量双提升</td>
                                    <td>海康/旷视</td>
                                    <td><span class="btn btn-success">0.85</span></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>公共卫生应急救治中心 5G+急救</td>
                                    <td>OneHealth 5G 急救平台+北斗定位</td>
                                    <td>政府规划必须项</td>
                                    <td>华为+移动联合体</td>
                                    <td><span class="btn btn-success">0.83</span></td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>DIP 大数据监管二期</td>
                                    <td>移动数智医疗 DIP 引擎+AI 审核</td>
                                    <td>一期由第三方交付</td>
                                    <td>可切入二期升级</td>
                                    <td><span class="btn btn-warning">0.78</span></td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>中医药博物馆 XR 数字展陈</td>
                                    <td>XR 云渲染+数字人讲解+移动云资源</td>
                                    <td>文化基地考核指标</td>
                                    <td>文创公司主导</td>
                                    <td><span class="btn btn-warning">0.70</span></td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>基层医联体云影像 PACS+AI</td>
                                    <td>移动云影像 SaaS+5G 切片</td>
                                    <td>已建中心需扩容</td>
                                    <td>联影/东软优势</td>
                                    <td><span class="btn btn-warning">0.68</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有章节
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // 添加active类到点击的按钮
            element.classList.add('active');

            // 控制二级导航显示
            const navSecondary = document.getElementById('nav-secondary');
            if (sectionId === 'section1') {
                navSecondary.classList.add('show');
            } else {
                navSecondary.classList.remove('show');
            }
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // 页面加载时显示第一个章节
        document.addEventListener('DOMContentLoaded', function() {
            const firstSection = document.getElementById('section1');
            if (firstSection) {
                firstSection.style.display = 'block';
                firstSection.classList.add('active');
            }

            // 显示二级导航
            const navSecondary = document.getElementById('nav-secondary');
            navSecondary.classList.add('show');
        });
    </script>
</body>
</html>
