<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：山东新和成控股有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            opacity: 0;
            animation: fadeInUp 0.8s ease forwards;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
            opacity: 0;
            animation: fadeInUp 0.8s ease 0.2s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 内容区域 */
        .content {
            padding: 30px;
        }

        .section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 40px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            padding-left: 12px;
            border-left: 4px solid #4a90a4;
        }

        /* 卡片系统 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #1d1d1f;
            font-weight: 600;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4, #6b73a0);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .rating-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .rating-stars {
            color: #ffd700;
            font-size: 18px;
            margin: 5px 0;
        }

        .rating-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .rating-content {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .person-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .person-desc {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.85;
        }

        /* 按钮系统 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-group {
            margin: 15px 0;
        }

        /* 表格系统 */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }

        .table th {
            background: #f8f9fa;
            color: #1d1d1f;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            border-bottom: 1px solid #e9ecef;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 13px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">一、战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">二、政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">三、行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">四、招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">五、商机类型</button>
            </div>
            
            <div class="nav-secondary" id="nav-secondary">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>山东新和成控股有限公司</h1>
            <p>企业画像报告</p>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 一、战客动态 -->
            <div id="section1" class="section active">
                <div class="section-title">一、战客动态</div>

                <!-- 1. 基础信息 -->
                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    
                    <div class="card">
                        <div class="card-title">企业基本信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">企业名称</div>
                                <div class="info-value">山东新和成控股有限公司（简称：山东新和成）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成立日期</div>
                                <div class="info-value">2017年2月10日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">企业属性</div>
                                <div class="info-value">有限责任公司（自然人投资或控股的法人独资）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">注册资金</div>
                                <div class="info-value">20000万元</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">实缴资金</div>
                                <div class="info-value">20000万元</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">经营地址</div>
                                <div class="info-value">山东省潍坊市滨海区央子街道珠江西街01156号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">隶属关系</div>
                                <div class="info-value">隶属于新和成控股集团</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">主要分支机构</div>
                                <div class="info-value">山东氨基酸有限公司、海外子公司（欧洲、南美、东南亚）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 价值度评级 -->
                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="rating-title">品牌价值评级</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-content">
                            <strong>评级依据：</strong>多次获得国家级及区域级奖项，如"全国工业和信息化系统先进集体"。在高端化工、智能制造等领域具有行业标杆地位。<br>
                            <strong>价值点：</strong>行业影响力强，品牌认可度高，具备较强市场竞争力。
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="rating-title">收益价值评级</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-content">
                            <strong>评级依据：</strong>2024年经营业绩创历史新高，净利润同比增长116.2%。海外业务占比超50%，全球化布局带来稳定收益。<br>
                            <strong>价值点：</strong>现有合作规模大，潜在数字化项目机会丰富，具备持续增长潜力。
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="rating-title">合作紧密度评级</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-content">
                            <strong>评级依据：</strong>已在多个领域展开深度合作，包括SAP系统部署、AI运营管控体系建设等。具备良好的履约能力和项目交付质量。<br>
                            <strong>价值点：</strong>战略合作基础稳固，具备长期合作意愿与能力。
                        </div>
                    </div>
                </div>

                <!-- 3. 经营画像 -->
                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>

                    <div class="card">
                        <div class="card-title">主营业务范围及核心产品/服务</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>高端化工品：</strong>蛋氨酸、维生素E、HDI等特种化学品<br>
                            <strong>新材料：</strong>尼龙新材料、PPS等<br>
                            <strong>数字化解决方案：</strong>智能工厂、"七剑系统"、生成式AI平台等<br>
                            <strong>国际化销售与技术服务</strong>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">产业链定位</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>上游：</strong>原材料供应（与中国石化合资液体蛋氨酸项目）<br>
                            <strong>中游：</strong>智能制造与生产优化<br>
                            <strong>下游：</strong>全球市场拓展与客户定制服务
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">收入规模及企业属性</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>收入规模：</strong>未知<br>
                            <strong>区域重点产业集群：</strong>是（潍坊市高端化工产业集群）<br>
                            <strong>规上企业：</strong>是<br>
                            <strong>专精特新企业：</strong>未知<br>
                            <strong>省级单项冠军：</strong>未知
                        </div>
                    </div>
                </div>

                <!-- 4. 行业画像 -->
                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">化工行业智能化转型领先者</span>
                            <span class="btn btn-success">潍坊市唯一入选企业</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            化工行业中智能化转型领先者；国家工信部授予"全国工业和信息化系统先进集体"，为潍坊市唯一入选企业。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">潍坊市2024年度科技创新标兵企业</span>
                            <span class="btn btn-success">潍坊市2024年度突出贡献企业</span>
                            <span class="btn btn-warning">全国工业和信息化系统先进集体</span>
                            <span class="btn btn-info">绍兴市民营企业研发投入10强</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>2025年2月：</strong>获"潍坊市2024年度科技创新标兵企业"、"潍坊市2024年度突出贡献企业"<br>
                            <strong>2025年5月：</strong>获"全国工业和信息化系统先进集体"、"绍兴市民营企业研发投入10强"
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">资质认证及专利情况</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>资质认证：</strong>规上企业、科技创新型企业<br>
                            <strong>专利/商标统计情况：</strong>未知
                        </div>
                    </div>
                </div>

                <!-- 5. 关键人 -->
                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>

                    <div class="person-card">
                        <div class="person-name">胡柏剡</div>
                        <div class="person-title">法人代表、副董事长兼总裁</div>
                        <div class="person-desc">负责企业战略规划、日常运营管理。联系方式：未知</div>
                    </div>

                    <div class="person-card">
                        <div class="person-name">胡柏藩</div>
                        <div class="person-title">实控人（董事长）</div>
                        <div class="person-desc">提出股份回购计划以提升股东回报及员工激励。母公司画像：新和成控股集团，总部位于浙江绍兴，是中国大型精细化工企业，业务覆盖营养健康、新材料、精细化工等多个领域。</div>
                    </div>

                    <div class="card">
                        <div class="card-title">对口业务部门负责人及对接人信息</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b5a9c;">
                            未知
                        </div>
                    </div>
                </div>

                <!-- 6. 主管单位 -->
                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>

                    <div class="card">
                        <div class="card-title">主要主管单位</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">所属主要行业主管条线</div>
                                <div class="info-value">化工、智能制造、数字经济</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">省级主管</div>
                                <div class="info-value">山东省工业和信息化厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">地方主管</div>
                                <div class="info-value">潍坊市滨海经济技术开发区管委会</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">国家级主管</div>
                                <div class="info-value">国家工业和信息化部（国家级奖项颁发单位）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 7. 重点项目 -->
                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">"七剑系统"智能工厂建设</span>
                            <span class="btn btn-success">应急指挥与人员定位平台</span>
                            <span class="btn btn-warning">智能立体仓库（山东基地）</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>"七剑系统"智能工厂建设：</strong>整合MES、EMS、WMS等七大系统，实现全流程自动化管理，降低生产成本3%以上。<br><br>
                            <strong>应急指挥与人员定位平台：</strong>铺设3600个信标，提升安全管控能力。<br><br>
                            <strong>智能立体仓库（山东基地）：</strong>应用旷视科技3A方案，仓储效率提升50%+。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>项目描述</th>
                                        <th>预期效果</th>
                                        <th>合作方</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>弱电系统框架维保项目</td>
                                        <td>招标实施阶段（2025-2027年）</td>
                                        <td>提升安防监控、综合布线等弱电系统稳定性</td>
                                        <td>未知</td>
                                    </tr>
                                    <tr>
                                        <td>海外子公司SAP系统建设项目</td>
                                        <td>启动阶段（2025年实施）</td>
                                        <td>实现跨国财务管理、供应链管理系统统一</td>
                                        <td>SAP公司</td>
                                    </tr>
                                    <tr>
                                        <td>生成式AI运营管控体系</td>
                                        <td>试点筹备阶段（2025年试点）</td>
                                        <td>目标降低生产成本5%+，缩短研发周期30%</td>
                                        <td>未知</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>技术方案</th>
                                        <th>应用场景</th>
                                        <th>效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>"七剑系统"智能工厂输出至天津尼龙新材料项目</td>
                                        <td>智能工厂系统集成</td>
                                        <td>天津尼龙新材料生产基地</td>
                                        <td>实现智能制造标准化输出</td>
                                    </tr>
                                    <tr>
                                        <td>生成式AI运营管控体系建设</td>
                                        <td>AI中台+工艺优化算法</td>
                                        <td>生产工艺优化、质量控制</td>
                                        <td>降低生产成本5%+，缩短研发周期30%</td>
                                    </tr>
                                    <tr>
                                        <td>SAP系统全球部署</td>
                                        <td>跨国ERP系统</td>
                                        <td>欧洲、南美、东南亚子公司</td>
                                        <td>实现全球业务统一管理</td>
                                    </tr>
                                    <tr>
                                        <td>液体蛋氨酸项目数字化协同平台</td>
                                        <td>数字化协同系统</td>
                                        <td>与中国石化合资项目</td>
                                        <td>提升项目协同效率</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 二、政策信息 -->
            <div id="section2" class="section">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">国家级政策</div>

                    <div class="card">
                        <div class="card-title">《"十四五"原材料工业发展规划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">工信部联原〔2021〕212号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工业和信息化部等三部门</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2021年12月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-primary">发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>推动原材料工业高质量发展，加快数字化转型，提升智能制造水平，促进绿色低碳发展。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">《石化化工行业稳增长工作方案》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">工信部联原〔2023〕176号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工业和信息化部等六部门</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2023年8月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-secondary">工作方案</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b93c0;">
                            <strong>主要内容：</strong>支持石化化工企业数字化转型，推进智能工厂建设，提升产业链供应链韧性。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">《山东省化工产业高质量发展规划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政发〔2023〕15号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2023年6月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #7b83b0;">
                            <strong>主要内容：</strong>推动化工产业向高端化、智能化、绿色化发展，支持精细化工企业技术创新和数字化转型。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 三、行业热点 -->
            <div id="section3" class="section">
                <div class="section-title">三、行业热点</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>热点方向</th>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>潜在商机</th>
                                    <th>信息来源</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="btn btn-primary">智能制造</span></td>
                                    <td>5G智能工厂建设</td>
                                    <td>生产效率提升20%，质量稳定性提高</td>
                                    <td>5G+工业互联网解决方案</td>
                                    <td>工信部智能制造示范工厂名单</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-success">绿色低碳</span></td>
                                    <td>清洁生产技术改造</td>
                                    <td>碳排放减少30%，能耗降低25%</td>
                                    <td>节能环保技术、碳管理平台</td>
                                    <td>生态环境部绿色工厂认定</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-warning">数字化转型</span></td>
                                    <td>工业互联网平台建设</td>
                                    <td>管理效率提升30%，成本降低15%</td>
                                    <td>企业级数字化解决方案</td>
                                    <td>山东省工业互联网平台</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-info">产业协同</span></td>
                                    <td>供应链数字化协同</td>
                                    <td>供应链响应速度提升40%</td>
                                    <td>供应链管理系统、区块链溯源</td>
                                    <td>中国化工信息中心</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 四、招投标信息 -->
            <div id="section4" class="section">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>标讯标题</th>
                                    <th>招标方式</th>
                                    <th>招标公司</th>
                                    <th>产品</th>
                                    <th>中标公司</th>
                                    <th>项目地区</th>
                                    <th>金额</th>
                                    <th>发布日期</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>5G专网建设项目</td>
                                    <td>邀请招标</td>
                                    <td>山东新和成控股有限公司</td>
                                    <td>5G专网设备及服务</td>
                                    <td>中国移动通信集团山东有限公司</td>
                                    <td>潍坊</td>
                                    <td>2800万元</td>
                                    <td>2024年3月</td>
                                </tr>
                                <tr>
                                    <td>工业互联网平台建设</td>
                                    <td>公开招标</td>
                                    <td>山东新和成控股有限公司</td>
                                    <td>工业互联网平台</td>
                                    <td>阿里云计算有限公司</td>
                                    <td>潍坊</td>
                                    <td>1500万元</td>
                                    <td>2024年1月</td>
                                </tr>
                                <tr>
                                    <td>智能安防系统升级</td>
                                    <td>竞争性谈判</td>
                                    <td>山东新和成控股有限公司</td>
                                    <td>AI视频监控系统</td>
                                    <td>海康威视数字技术股份有限公司</td>
                                    <td>潍坊</td>
                                    <td>800万元</td>
                                    <td>2023年11月</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 五、商机类型 -->
            <div id="section5" class="section">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机场景</th>
                                    <th>移动可交付方案</th>
                                    <th>客户痛点匹配</th>
                                    <th>竞争格局</th>
                                    <th>落地评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>能耗在线监测+碳管理平台</td>
                                    <td>OnePower双碳平台+5G工业网关</td>
                                    <td>节能审查整改刚需，政府监管高压</td>
                                    <td>需与西门子、和利时PK</td>
                                    <td><span class="btn btn-success">0.85</span></td>
                                </tr>
                                <tr>
                                    <td>5G+MES融合专网</td>
                                    <td>5G尊享专网+MES集成</td>
                                    <td>产线老旧、数据孤岛</td>
                                    <td>华为+运营商联合体优势明显</td>
                                    <td><span class="btn btn-success">0.78</span></td>
                                </tr>
                                <tr>
                                    <td>AI安防视频平台</td>
                                    <td>移动千里眼+AI算法仓</td>
                                    <td>香精香料易燃爆，安监环保双重要求</td>
                                    <td>海康、大华占先机，可切入存量改造</td>
                                    <td><span class="btn btn-warning">0.72</span></td>
                                </tr>
                                <tr>
                                    <td>云灾备+等保2.0</td>
                                    <td>移动云+等保一体机</td>
                                    <td>配方数据属核心机密</td>
                                    <td>阿里云、华为云已布局，需价格战</td>
                                    <td><span class="btn btn-warning">0.65</span></td>
                                </tr>
                                <tr>
                                    <td>数字孪生工厂</td>
                                    <td>BIM+实时数据+3D可视化</td>
                                    <td>高管KPI与"灯塔工厂"对标</td>
                                    <td>项目预算高，需集团总部推动</td>
                                    <td><span class="btn btn-warning">0.60</span></td>
                                </tr>
                                <tr>
                                    <td>供应链可视化SaaS</td>
                                    <td>移动OneSupply链融平台</td>
                                    <td>大宗原料价格波动大</td>
                                    <td>客户已有SAP/Oracle，替换难度大</td>
                                    <td><span class="btn btn-info">0.50</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有章节
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // 添加active类到点击的按钮
            element.classList.add('active');

            // 控制二级导航显示
            const navSecondary = document.getElementById('nav-secondary');
            if (sectionId === 'section1') {
                navSecondary.classList.add('show');
            } else {
                navSecondary.classList.remove('show');
            }
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // 页面加载时显示第一个章节
        document.addEventListener('DOMContentLoaded', function() {
            const firstSection = document.getElementById('section1');
            if (firstSection) {
                firstSection.style.display = 'block';
                firstSection.classList.add('active');
            }

            // 显示二级导航
            const navSecondary = document.getElementById('nav-secondary');
            navSecondary.classList.add('show');
        });
    </script>
</body>
</html>
