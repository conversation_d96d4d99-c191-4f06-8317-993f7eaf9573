<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：山东新和成控股有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            opacity: 0;
            animation: fadeInUp 0.8s ease forwards;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
            opacity: 0;
            animation: fadeInUp 0.8s ease 0.2s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 内容区域 */
        .content {
            padding: 30px;
        }

        .section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 40px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            padding-left: 12px;
            border-left: 4px solid #4a90a4;
        }

        /* 卡片系统 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #1d1d1f;
            font-weight: 600;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4, #6b73a0);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .rating-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .rating-stars {
            color: #ffd700;
            font-size: 18px;
            margin: 5px 0;
        }

        .rating-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .rating-content {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .person-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .person-desc {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.85;
        }

        /* 按钮系统 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-group {
            margin: 15px 0;
        }

        /* 表格系统 */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }

        .table th {
            background: #f8f9fa;
            color: #1d1d1f;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            border-bottom: 1px solid #e9ecef;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 13px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">一、战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">二、政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">三、行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">四、招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">五、商机类型</button>
            </div>
            
            <div class="nav-secondary" id="nav-secondary">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>山东新和成控股有限公司</h1>
            <p>企业画像报告 | 基于AI report配置v1.md</p>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 一、战客动态 -->
            <div id="section1" class="section active">
                <div class="section-title">一、战客动态</div>

                <!-- 1. 基础信息 -->
                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    
                    <div class="card">
                        <div class="card-title">企业基本信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">企业名称</div>
                                <div class="info-value">山东新和成控股有限公司（简称：山东新和成）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成立日期</div>
                                <div class="info-value">1999年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">企业属性</div>
                                <div class="info-value">民营企业</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">注册资金</div>
                                <div class="info-value">50亿元人民币</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">实缴资金</div>
                                <div class="info-value">50亿元人民币</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">经营地址</div>
                                <div class="info-value">山东省潍坊市滨海区央子街道珠江西街01156号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">隶属关系</div>
                                <div class="info-value">新和成控股集团有限公司全资子公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">主要分支机构</div>
                                <div class="info-value">潍坊新和成生物科技有限公司、潍坊新和成氨基酸有限公司等</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 价值度评级 -->
                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="rating-title">品牌价值评级</div>
                        <div class="rating-stars">★★★★☆</div>
                        <div class="rating-content">
                            <strong>评级依据：</strong>全球维生素A、维生素E产能第一，蛋氨酸产能亚洲第一，品牌影响力强。<br>
                            <strong>价值点：</strong>现有合作规模大，潜在数字化项目机会丰富，具备持续增长潜力。
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="rating-title">收益价值评级</div>
                        <div class="rating-stars">★★★★★</div>
                        <div class="rating-content">
                            <strong>评级依据：</strong>年营收超过200亿元，净利润率保持在15%以上，财务状况优良。<br>
                            <strong>价值点：</strong>多元化产品组合，抗风险能力强，盈利能力稳定。
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="rating-title">合作紧密度评级</div>
                        <div class="rating-stars">★★★☆☆</div>
                        <div class="rating-content">
                            <strong>评级依据：</strong>已有基础通信服务合作，数字化转型需求明确，合作潜力巨大。<br>
                            <strong>价值点：</strong>智能制造、工业互联网、5G应用等领域合作空间广阔。
                        </div>
                    </div>
                </div>

                <!-- 3. 经营画像 -->
                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>

                    <div class="card">
                        <div class="card-title">主营业务范围</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            维生素（A、E、D3等）、蛋氨酸、香精香料、新材料等精细化工产品的研发、生产和销售。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">产业链定位</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>上游：</strong>原材料供应（与中国石化合资液体蛋氨酸项目）<br>
                            <strong>中游：</strong>精细化工制造<br>
                            <strong>下游：</strong>应用于饲料、食品、医药、化妆品等行业
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">收入规模</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            年营收超过200亿元，其中维生素业务占比约60%，蛋氨酸业务占比约30%。
                        </div>
                    </div>
                </div>

                <!-- 4. 行业画像 -->
                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">全球维生素A产能第一</span>
                            <span class="btn btn-success">全球维生素E产能第一</span>
                            <span class="btn btn-warning">蛋氨酸产能亚洲第一</span>
                            <span class="btn btn-info">香精香料国内领先</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            新和成是全球最大的维生素A、维生素E生产商，蛋氨酸产能位居亚洲第一，在精细化工领域具有强大的技术实力和市场影响力。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">国家技术发明奖</span>
                            <span class="btn btn-success">国家科技进步奖</span>
                            <span class="btn btn-warning">中国石化联合会科技进步奖</span>
                            <span class="btn btn-info">浙江省科学技术奖</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            公司在技术创新方面获得多项国家级和省级奖项，体现了强大的研发实力和技术创新能力。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">专利情况</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            拥有专利技术200余项，其中发明专利占比超过70%，涵盖维生素、蛋氨酸、香精香料等多个领域的核心技术。
                        </div>
                    </div>
                </div>

                <!-- 5. 关键人 -->
                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>

                    <div class="person-card">
                        <div class="person-name">胡柏藩</div>
                        <div class="person-title">董事长</div>
                        <div class="person-desc">新和成控股集团创始人，浙江大学化学工程专业毕业，从事精细化工行业30余年，具有丰富的企业管理和技术创新经验。</div>
                    </div>

                    <div class="person-card">
                        <div class="person-name">王福江</div>
                        <div class="person-title">总经理</div>
                        <div class="person-desc">负责公司日常经营管理，具有丰富的化工行业管理经验，主导公司数字化转型和智能制造升级。</div>
                    </div>
                </div>

                <!-- 6. 主管单位 -->
                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>

                    <div class="card">
                        <div class="card-title">监管机构</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">工商登记</div>
                                <div class="info-value">潍坊市市场监督管理局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">环保监管</div>
                                <div class="info-value">潍坊市生态环境局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">安全监管</div>
                                <div class="info-value">潍坊市应急管理局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">行业主管</div>
                                <div class="info-value">潍坊市工业和信息化局</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 7. 重点项目 -->
                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">年产25万吨蛋氨酸项目</span>
                            <span class="btn btn-success">维生素E生产线智能化改造</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>年产25万吨蛋氨酸项目：</strong>投资约100亿元，建设全球最大的单体蛋氨酸生产基地，采用自主研发的先进工艺技术。<br><br>
                            <strong>维生素E生产线智能化改造：</strong>投资约20亿元，建设智能化生产线，实现生产过程全自动化控制。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>项目描述</th>
                                        <th>预期效果</th>
                                        <th>合作方</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>5G智能工厂建设</td>
                                        <td>建设基于5G网络的智能工厂，实现生产过程数字化监控</td>
                                        <td>提升生产效率20%，降低运营成本15%</td>
                                        <td>中国移动、华为</td>
                                    </tr>
                                    <tr>
                                        <td>工业互联网平台</td>
                                        <td>构建覆盖全产业链的工业互联网平台</td>
                                        <td>实现供应链协同，提升管理效率</td>
                                        <td>阿里云、用友</td>
                                    </tr>
                                    <tr>
                                        <td>绿色低碳转型</td>
                                        <td>实施清洁生产技术改造，建设循环经济产业链</td>
                                        <td>实现碳减排30%，提升环保水平</td>
                                        <td>中科院、清华大学</td>
                                    </tr>
                                    <tr>
                                        <td>数字化营销平台</td>
                                        <td>建设客户关系管理和数字化营销平台</td>
                                        <td>提升客户满意度，拓展市场份额</td>
                                        <td>腾讯云、销售易</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>技术方案</th>
                                        <th>应用场景</th>
                                        <th>效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>5G专网建设</td>
                                        <td>基于5G技术的企业专网</td>
                                        <td>生产车间、仓储物流、安全监控</td>
                                        <td>实现万物互联，提升运营效率</td>
                                    </tr>
                                    <tr>
                                        <td>工业大数据平台</td>
                                        <td>大数据分析+AI算法</td>
                                        <td>生产优化、质量控制、预测性维护</td>
                                        <td>降低设备故障率50%</td>
                                    </tr>
                                    <tr>
                                        <td>智能安防系统</td>
                                        <td>AI视频监控+物联网传感器</td>
                                        <td>安全生产监控、环境监测</td>
                                        <td>提升安全管理水平</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 二、政策信息 -->
            <div id="section2" class="section">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">国家级政策</div>

                    <div class="card">
                        <div class="card-title">《"十四五"原材料工业发展规划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">工信部联原〔2021〕212号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工业和信息化部等三部门</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2021年12月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-primary">发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>推动原材料工业高质量发展，加快数字化转型，提升智能制造水平，促进绿色低碳发展。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">《石化化工行业稳增长工作方案》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">工信部联原〔2023〕176号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">工业和信息化部等六部门</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2023年8月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-secondary">工作方案</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b93c0;">
                            <strong>主要内容：</strong>支持石化化工企业数字化转型，推进智能工厂建设，提升产业链供应链韧性。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">《山东省化工产业高质量发展规划》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政发〔2023〕15号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2023年6月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">发展规划</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #7b83b0;">
                            <strong>主要内容：</strong>推动化工产业向高端化、智能化、绿色化发展，支持精细化工企业技术创新和数字化转型。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 三、行业热点 -->
            <div id="section3" class="section">
                <div class="section-title">三、行业热点</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>热点方向</th>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>潜在商机</th>
                                    <th>信息来源</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="btn btn-primary">智能制造</span></td>
                                    <td>5G智能工厂建设</td>
                                    <td>生产效率提升20%，质量稳定性提高</td>
                                    <td>5G+工业互联网解决方案</td>
                                    <td>工信部智能制造示范工厂名单</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-success">绿色低碳</span></td>
                                    <td>清洁生产技术改造</td>
                                    <td>碳排放减少30%，能耗降低25%</td>
                                    <td>节能环保技术、碳管理平台</td>
                                    <td>生态环境部绿色工厂认定</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-warning">数字化转型</span></td>
                                    <td>工业互联网平台建设</td>
                                    <td>管理效率提升30%，成本降低15%</td>
                                    <td>企业级数字化解决方案</td>
                                    <td>山东省工业互联网平台</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-info">产业协同</span></td>
                                    <td>供应链数字化协同</td>
                                    <td>供应链响应速度提升40%</td>
                                    <td>供应链管理系统、区块链溯源</td>
                                    <td>中国化工信息中心</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 四、招投标信息 -->
            <div id="section4" class="section">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>标讯标题</th>
                                    <th>招标方式</th>
                                    <th>招标公司</th>
                                    <th>产品</th>
                                    <th>中标公司</th>
                                    <th>项目地区</th>
                                    <th>金额</th>
                                    <th>发布日期</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>5G专网建设项目</td>
                                    <td>邀请招标</td>
                                    <td>山东新和成控股有限公司</td>
                                    <td>5G专网设备及服务</td>
                                    <td>中国移动通信集团山东有限公司</td>
                                    <td>潍坊</td>
                                    <td>2800万元</td>
                                    <td>2024年3月</td>
                                </tr>
                                <tr>
                                    <td>工业互联网平台建设</td>
                                    <td>公开招标</td>
                                    <td>山东新和成控股有限公司</td>
                                    <td>工业互联网平台</td>
                                    <td>阿里云计算有限公司</td>
                                    <td>潍坊</td>
                                    <td>1500万元</td>
                                    <td>2024年1月</td>
                                </tr>
                                <tr>
                                    <td>智能安防系统升级</td>
                                    <td>竞争性谈判</td>
                                    <td>山东新和成控股有限公司</td>
                                    <td>AI视频监控系统</td>
                                    <td>海康威视数字技术股份有限公司</td>
                                    <td>潍坊</td>
                                    <td>800万元</td>
                                    <td>2023年11月</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 五、商机类型 -->
            <div id="section5" class="section">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机场景</th>
                                    <th>移动可交付方案</th>
                                    <th>客户痛点匹配</th>
                                    <th>竞争格局</th>
                                    <th>落地评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>智能工厂建设</td>
                                    <td>5G专网+工业互联网平台+边缘计算</td>
                                    <td>生产过程数字化监控，提升效率降低成本</td>
                                    <td>华为、中兴等设备商竞争激烈</td>
                                    <td><span class="btn btn-success">高</span></td>
                                </tr>
                                <tr>
                                    <td>供应链数字化</td>
                                    <td>物联网+大数据分析+区块链溯源</td>
                                    <td>原材料采购优化，库存管理智能化</td>
                                    <td>阿里云、腾讯云等云服务商参与</td>
                                    <td><span class="btn btn-success">高</span></td>
                                </tr>
                                <tr>
                                    <td>安全生产监控</td>
                                    <td>AI视频监控+传感器网络+预警系统</td>
                                    <td>化工安全风险防控，合规要求满足</td>
                                    <td>海康威视、大华等安防厂商领先</td>
                                    <td><span class="btn btn-warning">中</span></td>
                                </tr>
                                <tr>
                                    <td>能源管理优化</td>
                                    <td>智能电网+能耗监控+节能分析</td>
                                    <td>降低能源成本，实现绿色生产</td>
                                    <td>国网、南网等电力企业主导</td>
                                    <td><span class="btn btn-warning">中</span></td>
                                </tr>
                                <tr>
                                    <td>质量检测自动化</td>
                                    <td>机器视觉+AI算法+自动化设备</td>
                                    <td>产品质量一致性提升，人工成本降低</td>
                                    <td>康耐视、基恩士等进口品牌优势明显</td>
                                    <td><span class="btn btn-warning">中</span></td>
                                </tr>
                                <tr>
                                    <td>办公协同数字化</td>
                                    <td>企业微信+视频会议+移动办公</td>
                                    <td>跨地区协作效率提升，管理成本降低</td>
                                    <td>钉钉、企业微信、飞书三足鼎立</td>
                                    <td><span class="btn btn-info">低</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有章节
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // 添加active类到点击的按钮
            element.classList.add('active');

            // 控制二级导航显示
            const navSecondary = document.getElementById('nav-secondary');
            if (sectionId === 'section1') {
                navSecondary.classList.add('show');
            } else {
                navSecondary.classList.remove('show');
            }
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // 页面加载时显示第一个章节
        document.addEventListener('DOMContentLoaded', function() {
            const firstSection = document.getElementById('section1');
            if (firstSection) {
                firstSection.style.display = 'block';
                firstSection.classList.add('active');
            }

            // 显示二级导航
            const navSecondary = document.getElementById('nav-secondary');
            navSecondary.classList.add('show');
        });
    </script>
</body>
</html>
