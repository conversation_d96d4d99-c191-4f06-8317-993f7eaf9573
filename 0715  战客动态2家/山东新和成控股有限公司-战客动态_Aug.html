<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：山东新和成控股有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            opacity: 0;
            animation: fadeInUp 0.8s ease forwards;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
            opacity: 0;
            animation: fadeInUp 0.8s ease 0.2s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 内容区域 */
        .content {
            padding: 30px;
        }

        .section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #4a90a4;
        }

        .subsection {
            margin-bottom: 40px;
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            padding-left: 12px;
            border-left: 4px solid #4a90a4;
        }

        /* 卡片系统 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #1d1d1f;
            font-weight: 600;
        }

        /* 评级卡片 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4, #6b73a0);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .rating-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .rating-stars {
            color: #ffd700;
            font-size: 18px;
            margin-left: 10px;
        }

        .rating-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rating-details {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .person-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .person-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .person-desc {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.85;
        }

        /* 按钮系统 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #4a90a4; color: white; }
        .btn-success { background: #5ba3b4; color: white; }
        .btn-warning { background: #6b73a0; color: white; }
        .btn-info { background: #7b83b0; color: white; }
        .btn-secondary { background: #8b93c0; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-group {
            margin: 15px 0;
        }

        /* 表格系统 */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }

        .table th {
            background: #f8f9fa;
            color: #1d1d1f;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            border-bottom: 1px solid #e9ecef;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 13px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-container">
            <div class="nav-primary">
                <button class="nav-item active" onclick="showSection('section1', this)">一、战客动态</button>
                <button class="nav-item" onclick="showSection('section2', this)">二、政策信息</button>
                <button class="nav-item" onclick="showSection('section3', this)">三、行业热点</button>
                <button class="nav-item" onclick="showSection('section4', this)">四、招投标信息</button>
                <button class="nav-item" onclick="showSection('section5', this)">五、商机类型</button>
            </div>
            
            <div class="nav-secondary" id="nav-secondary">
                <button class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</button>
                <button class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</button>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>山东新和成控股有限公司</h1>
            <p>企业画像报告</p>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 一、战客动态 -->
            <div id="section1" class="section active">
                <div class="section-title">一、战客动态</div>

                <!-- 1. 基础信息 -->
                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    
                    <div class="card">
                        <div class="card-title">企业基本信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">企业名称</div>
                                <div class="info-value">山东新和成控股有限公司（简称：山东新和成）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成立日期</div>
                                <div class="info-value">2017年2月10日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">企业属性</div>
                                <div class="info-value">有限责任公司（自然人投资或控股的法人独资）</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">注册资金</div>
                                <div class="info-value">20000万元</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">实缴资金</div>
                                <div class="info-value">20000万元</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">经营地址</div>
                                <div class="info-value">山东省潍坊市滨海区央子街道珠江西街01156号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">隶属关系</div>
                                <div class="info-value">隶属于新和成控股集团</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">主要分支机构</div>
                                <div class="info-value">山东氨基酸有限公司、海外子公司（欧洲、南美、东南亚）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 价值度评级 -->
                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>多次获得国家级及区域级奖项，如"全国工业和信息化系统先进集体"。在高端化工、智能制造等领域具有行业标杆地位。<br>
                                <strong>价值点：</strong>行业影响力强，品牌认可度高，具备较强市场竞争力。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>2024年经营业绩创历史新高，净利润同比增长116.2%。海外业务占比超50%，全球化布局带来稳定收益。<br>
                                <strong>价值点：</strong>现有合作规模大，潜在数字化项目机会丰富，具备持续增长潜力。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>已在多个领域展开深度合作，包括SAP系统部署、AI运营管控体系建设等。具备良好的履约能力和项目交付质量。<br>
                                <strong>价值点：</strong>战略合作基础稳固，具备长期合作意愿与能力。
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3. 经营画像 -->
                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">高端化工品（蛋氨酸、维生素E、HDI等特种化学品）；新材料（尼龙新材料、PPS等）；数字化解决方案（智能工厂、"七剑系统"、生成式AI平台等）；国际化销售与技术服务。</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">上游原材料供应（与中国石化合资液体蛋氨酸项目）；中游智能制造与生产优化；下游全球市场拓展与客户定制服务。</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">未知</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群（潍坊市高端化工产业集群）</span>
                        <span class="btn btn-success">规上企业</span>
                        <span class="btn btn-secondary">专精特新企业（未知）</span>
                        <span class="btn btn-secondary">省级单项冠军（未知）</span>
                    </div>
                </div>

                <!-- 4. 行业画像 -->
                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">化工行业中智能化转型领先者</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">国家工信部授予"全国工业和信息化系统先进集体"，为潍坊市唯一入选企业</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">潍坊市2024年度科技创新标兵企业（2025年2月）</span>
                            <span class="btn btn-success">潍坊市2024年度突出贡献企业（2025年2月）</span>
                            <span class="btn btn-warning">全国工业和信息化系统先进集体（2025年5月）</span>
                            <span class="btn btn-info">绍兴市民营企业研发投入10强（2025年5月）</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">资质认证</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">资质认证</div>
                                <div class="info-value">规上企业；科技创新型企业</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">专利/商标统计情况</div>
                                <div class="info-value">未知</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 5. 关键人 -->
                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>

                    <div class="person-card">
                        <div class="person-name">胡柏剡</div>
                        <div class="person-title">法人代表、副董事长兼总裁</div>
                        <div class="person-desc">负责企业战略规划、日常运营管理。联系方式：未知</div>
                    </div>

                    <div class="person-card">
                        <div class="person-name">胡柏藩</div>
                        <div class="person-title">实控人（董事长）</div>
                        <div class="person-desc">提出股份回购计划以提升股东回报及员工激励。</div>
                    </div>

                    <div class="card">
                        <div class="card-title">母公司画像</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            新和成控股集团，总部位于浙江绍兴，是中国大型精细化工企业，业务覆盖营养健康、新材料、精细化工等多个领域。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">对口业务部门负责人及对接人信息</div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b5a9c;">
                            未知
                        </div>
                    </div>
                </div>

                <!-- 6. 主管单位 -->
                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>

                    <div class="card">
                        <div class="card-title">主要主管单位</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">所属主要行业主管条线</div>
                                <div class="info-value">化工、智能制造、数字经济</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">省级主管</div>
                                <div class="info-value">山东省工业和信息化厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">地方主管</div>
                                <div class="info-value">潍坊市滨海经济技术开发区管委会</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">国家级主管</div>
                                <div class="info-value">国家工业和信息化部（国家级奖项颁发单位）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 7. 重点项目 -->
                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">"七剑系统"智能工厂建设</span>
                            <span class="btn btn-success">应急指挥与人员定位平台</span>
                            <span class="btn btn-warning">智能立体仓库（山东基地）</span>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>"七剑系统"智能工厂建设：</strong>整合MES、EMS、WMS等七大系统，实现全流程自动化管理，降低生产成本3%以上。<br><br>
                            <strong>应急指挥与人员定位平台：</strong>铺设3600个信标，提升安全管控能力。<br><br>
                            <strong>智能立体仓库（山东基地）：</strong>应用旷视科技3A方案，仓储效率提升50%+。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="subsection-title" style="font-size: 16px; margin: 20px 0 15px 0;">已立项项目的推进阶段：</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>推进阶段</th>
                                        <th>项目内容</th>
                                        <th>配套需求</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>弱电系统框架维保项目</td>
                                        <td>招标实施阶段（2025-2027年）</td>
                                        <td>安防监控、综合布线等弱电系统维护</td>
                                        <td>弱电系统维护服务</td>
                                    </tr>
                                    <tr>
                                        <td>海外子公司SAP系统建设项目</td>
                                        <td>启动阶段（2025年实施）</td>
                                        <td>跨国财务管理、供应链管理系统</td>
                                        <td>SAP系统实施与维护</td>
                                    </tr>
                                    <tr>
                                        <td>生成式AI运营管控体系</td>
                                        <td>试点筹备阶段（2025年试点）</td>
                                        <td>AI中台建设，工艺优化与质量控制</td>
                                        <td>AI算法开发与部署</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="subsection-title" style="font-size: 16px; margin: 20px 0 15px 0;">待开发项目的商机价值评级：</div>
                        <div class="btn-group">
                            <span class="btn btn-warning">数据驱动决策平台：★★★☆☆</span>
                            <span class="btn btn-warning">海外子公司ERP云平台：★★★☆☆</span>
                            <span class="btn btn-warning">AI质检系统：★★★☆☆</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>技术方案</th>
                                        <th>应用场景</th>
                                        <th>效果</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>"七剑系统"智能工厂输出至天津尼龙新材料项目</td>
                                        <td>智能工厂系统集成</td>
                                        <td>天津尼龙新材料生产基地</td>
                                        <td>实现智能制造标准化输出</td>
                                    </tr>
                                    <tr>
                                        <td>生成式AI运营管控体系建设</td>
                                        <td>AI中台+工艺优化算法</td>
                                        <td>生产工艺优化、质量控制</td>
                                        <td>降低生产成本5%+，缩短研发周期30%</td>
                                    </tr>
                                    <tr>
                                        <td>SAP系统全球部署</td>
                                        <td>跨国ERP系统</td>
                                        <td>欧洲、南美、东南亚子公司</td>
                                        <td>实现全球业务统一管理</td>
                                    </tr>
                                    <tr>
                                        <td>液体蛋氨酸项目数字化协同平台</td>
                                        <td>数字化协同系统</td>
                                        <td>与中国石化合资项目</td>
                                        <td>提升项目协同效率</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 二、政策信息 -->
            <div id="section2" class="section">
                <div class="section-title">二、政策信息</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">关于建立财政金融协同联动机制全力支持乡村振兴的实施意见</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">潍财金〔2024〕12号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市财政局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-primary">区域经济发展类</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>支持农业供应链金融发展；推动高标准农田物联网监测；鼓励冷链物流网络建设；支持绿色能源监测平台建设。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">国家级政策</div>

                    <div class="card">
                        <div class="card-title">《关于进一步促进经济稳健向好、进中提质的若干政策措施》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政发〔2024〕10号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-secondary">宏观经济调控类</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #8b93c0;">
                            <strong>主要内容：</strong>支持首台套技术装备、首批次新材料、首版次高端软件；鼓励新能源装机、新型储能项目建设；加快城市更新与智慧社区建设。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">山东省人民政府办公厅印发《科技创新引领标志性产业链高质量发展实施方案（2024—2027年）》</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政办发〔2024〕8号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省人民政府办公厅</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">产业发展规划类</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #7b83b0;">
                            <strong>主要内容：</strong>推动新一代信息技术、高端装备、新能源装备、先进材料等产业链发展；支持企业牵头重大科技项目比例达到80%以上；建设省级实验室、概念验证中心及中试基地。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 三、行业热点 -->
            <div id="section3" class="section">
                <div class="section-title">三、行业热点</div>

                <div class="subsection">
                    <div class="card">
                        <div class="card-title">热点方向</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">智能制造：推广"七剑系统"模式，推动智能工厂标准化输出</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">人工智能融合：构建生成式AI中台，应用于工艺优化与质量控制</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">绿色低碳：打造零碳工厂，推动节能减排与循环经济</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #8b5a9c;">国际化布局：加强新加坡、日本研发中心建设，拓展欧洲、南美、东南亚市场</li>
                        </ul>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>信息来源</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>"七剑系统"智能工厂建设</td>
                                    <td>实现核心装置"零手动操作"，生产成本降低3%以上</td>
                                    <td>山东新和成2025年工作规划重点（时间：2025年）</td>
                                </tr>
                                <tr>
                                    <td>生成式AI运营管控体系建设</td>
                                    <td>目标降低生产成本5%+，缩短研发周期30%</td>
                                    <td>山东新和成2025年工作规划重点（时间：2025年）</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="card">
                        <div class="card-title">潜在商机</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">智能制造解决方案输出：可复制"七剑系统"至其他制造企业</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">AI质检与工艺优化平台：面向化工、新材料等行业提供定制化AI服务</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">SaaS型企业管理平台：基于SAP系统经验，提供中小企业ERP云服务</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #8b5a9c;">绿色能源监测平台：结合碳减排政策，提供能耗监控与碳足迹追踪服务</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 四、招投标信息 -->
            <div id="section4" class="section">
                <div class="section-title">四、政采公示/招投标信息</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>标讯标题</th>
                                    <th>招标方式</th>
                                    <th>招标公司</th>
                                    <th>产品</th>
                                    <th>中标公司</th>
                                    <th>项目地区</th>
                                    <th>金额</th>
                                    <th>发布日期</th>
                                    <th>链接</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>弱电系统框架维保项目</td>
                                    <td>公开招标</td>
                                    <td>未知</td>
                                    <td>安防监控、综合布线等弱电系统维护</td>
                                    <td>未知</td>
                                    <td>山东省潍坊市</td>
                                    <td>未知</td>
                                    <td>2025年</td>
                                    <td>未知</td>
                                </tr>
                                <tr>
                                    <td>海外子公司SAP系统建设项目</td>
                                    <td>定向招标</td>
                                    <td>未知</td>
                                    <td>跨国财务管理、供应链管理系统</td>
                                    <td>SAP公司</td>
                                    <td>欧洲、南美、东南亚</td>
                                    <td>未公开（重大投资）</td>
                                    <td>2025年</td>
                                    <td>未知</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 五、商机类型 -->
            <div id="section5" class="section">
                <div class="section-title">五、商机类型</div>
                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商机场景</th>
                                    <th>移动可交付方案</th>
                                    <th>客户痛点匹配</th>
                                    <th>竞争格局</th>
                                    <th>落地评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>能耗在线监测+碳管理平台</td>
                                    <td>OnePower双碳平台+5G工业网关</td>
                                    <td>节能审查整改刚需，政府监管高压</td>
                                    <td>需与西门子、和利时PK</td>
                                    <td><span class="btn btn-success">0.85</span></td>
                                </tr>
                                <tr>
                                    <td>5G+MES融合专网</td>
                                    <td>5G尊享专网+MES集成</td>
                                    <td>产线老旧、数据孤岛</td>
                                    <td>华为+运营商联合体优势明显</td>
                                    <td><span class="btn btn-success">0.78</span></td>
                                </tr>
                                <tr>
                                    <td>AI安防视频平台</td>
                                    <td>移动千里眼+AI算法仓</td>
                                    <td>香精香料易燃爆，安监环保双重要求</td>
                                    <td>海康、大华占先机，可切入存量改造</td>
                                    <td><span class="btn btn-warning">0.72</span></td>
                                </tr>
                                <tr>
                                    <td>云灾备+等保2.0</td>
                                    <td>移动云+等保一体机</td>
                                    <td>配方数据属核心机密</td>
                                    <td>阿里云、华为云已布局，需价格战</td>
                                    <td><span class="btn btn-warning">0.65</span></td>
                                </tr>
                                <tr>
                                    <td>数字孪生工厂</td>
                                    <td>BIM+实时数据+3D可视化</td>
                                    <td>高管KPI与"灯塔工厂"对标</td>
                                    <td>项目预算高，需集团总部推动</td>
                                    <td><span class="btn btn-warning">0.60</span></td>
                                </tr>
                                <tr>
                                    <td>供应链可视化SaaS</td>
                                    <td>移动OneSupply链融平台</td>
                                    <td>大宗原料价格波动大</td>
                                    <td>客户已有SAP/Oracle，替换难度大</td>
                                    <td><span class="btn btn-info">0.50</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            // 隐藏所有章节
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // 移除所有按钮的active类
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // 添加active类到点击的按钮
            element.classList.add('active');

            // 控制二级导航显示
            const navSecondary = document.getElementById('nav-secondary');
            if (sectionId === 'section1') {
                navSecondary.classList.add('show');
            } else {
                navSecondary.classList.remove('show');
            }
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // 页面加载时显示第一个章节
        document.addEventListener('DOMContentLoaded', function() {
            const firstSection = document.getElementById('section1');
            if (firstSection) {
                firstSection.style.display = 'block';
                firstSection.classList.add('active');
            }

            // 显示二级导航
            const navSecondary = document.getElementById('nav-secondary');
            navSecondary.classList.add('show');
        });
    </script>
</body>
</html>
