<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业画像报告：山东华建铝业集团有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            font-size: 14px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部导航 */
        .nav-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-primary {
            display: flex;
            justify-content: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            padding: 8px 20px;
            margin: 0 4px;
            border-radius: 20px;
            background: transparent;
            border: none;
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background: #4a90a4;
            color: white;
            transform: translateY(-1px);
        }

        .nav-secondary {
            display: none;
            justify-content: center;
            padding: 12px 0;
            background: rgba(74, 144, 164, 0.05);
        }

        .nav-secondary.show {
            display: flex;
        }

        .nav-sub-item {
            padding: 6px 16px;
            margin: 0 2px;
            border-radius: 16px;
            background: transparent;
            border: none;
            color: #4a90a4;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-sub-item:hover {
            background: rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        /* 头部 */
        .header {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 内容区域 */
        .content {
            padding: 0 30px 30px;
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 30px 0 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4a90a4;
            letter-spacing: -0.3px;
        }

        .subsection {
            margin-bottom: 30px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .subsection-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .subsection-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #4a90a4;
            border-radius: 2px;
            margin-right: 12px;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 6px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            color: #1d1d1f;
            font-weight: 500;
            font-size: 14px;
        }

        /* 评级样式 */
        .rating-card {
            background: linear-gradient(135deg, #4a90a4 0%, #5ba3b4 50%, #6cb6c4 100%);
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .rating-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .rating-card .content {
            position: relative;
            z-index: 1;
        }

        .rating-card:nth-child(2) {
            background: linear-gradient(135deg, #5ba3b4 0%, #6cb6c4 50%, #7dc9d4 100%);
        }

        .rating-card:nth-child(3) {
            background: linear-gradient(135deg, #6b73a0 0%, #7b83b0 50%, #8b93c0 100%);
        }

        .rating-card:nth-child(4) {
            background: linear-gradient(135deg, #8b5a9c 0%, #9b6aac 50%, #ab7abc 100%);
        }

        .rating-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rating-stars {
            color: #ffd700;
            font-size: 18px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .rating-details {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 16px 0;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #4a90a4;
            color: white;
        }

        .btn-primary:hover {
            background: #3a7a94;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #5ba3b4;
            color: white;
        }

        .btn-warning {
            background: #6b73a0;
            color: white;
        }

        .btn-info {
            background: #7b83b0;
            color: white;
        }

        .btn-secondary {
            background: #8b93c0;
            color: white;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin: 20px 0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #1d1d1f;
            font-size: 13px;
            border-bottom: 1px solid #e9ecef;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f2f2f7;
            font-size: 13px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .card-title::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #4a90a4;
            border-radius: 50%;
            margin-right: 10px;
        }

        /* 人员卡片 */
        .person-card {
            background: linear-gradient(135deg, #4a90a4 0%, #6b73a0 50%, #8b5a9c 100%);
            color: white;
            border-radius: 16px;
            padding: 20px;
            margin: 12px 0;
            position: relative;
            overflow: hidden;
        }

        .person-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .person-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .person-role {
            font-size: 13px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .person-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 评分样式 */
        .score-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .score-high {
            background: linear-gradient(135deg, #5ba3b4, #6cb6c4);
        }

        .score-medium {
            background: linear-gradient(135deg, #6b73a0, #7b83b0);
        }

        .score-low {
            background: linear-gradient(135deg, #8b5a9c, #9b6aac);
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            .container {
                margin: 20px;
                max-width: calc(100% - 40px);
            }
        }

        @media (max-width: 768px) {
            .nav-primary {
                flex-wrap: wrap;
                padding: 12px;
            }
            
            .nav-item {
                font-size: 12px;
                padding: 6px 12px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 0 20px 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a90a4;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3a7a94;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-container">
            <div class="nav-primary">
                <a href="#section1" class="nav-item active" onclick="showSection('section1', this)">战客动态</a>
                <a href="#section2" class="nav-item" onclick="showSection('section2', this)">政策信息</a>
                <a href="#section3" class="nav-item" onclick="showSection('section3', this)">行业热点</a>
                <a href="#section4" class="nav-item" onclick="showSection('section4', this)">招投标信息</a>
                <a href="#section5" class="nav-item" onclick="showSection('section5', this)">商机类型</a>
            </div>
            <div class="nav-secondary" id="nav-secondary">
                <a href="#sub1-1" class="nav-sub-item" onclick="scrollToElement('sub1-1')">基础信息</a>
                <a href="#sub1-2" class="nav-sub-item" onclick="scrollToElement('sub1-2')">价值度评级</a>
                <a href="#sub1-3" class="nav-sub-item" onclick="scrollToElement('sub1-3')">经营画像</a>
                <a href="#sub1-4" class="nav-sub-item" onclick="scrollToElement('sub1-4')">行业画像</a>
                <a href="#sub1-5" class="nav-sub-item" onclick="scrollToElement('sub1-5')">关键人</a>
                <a href="#sub1-6" class="nav-sub-item" onclick="scrollToElement('sub1-6')">主管单位</a>
                <a href="#sub1-7" class="nav-sub-item" onclick="scrollToElement('sub1-7')">重点项目</a>
            </div>
        </div>

        <!-- 头部 -->
        <div class="header">
            <h1>企业画像报告</h1>
            <div class="subtitle">山东华建铝业集团有限公司</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 战客动态 -->
            <div id="section1" class="section">
                <div class="section-title">一、战客动态</div>
                
                <div id="sub1-1" class="subsection">
                    <div class="subsection-title">1. 基础信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">企业名称（简称）</div>
                            <div class="info-value">山东华建铝业集团有限公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">成立日期</div>
                            <div class="info-value">2001-04-05</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">企业属性</div>
                            <div class="info-value">有限责任公司（自然人投资或控股）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">注册资金</div>
                            <div class="info-value">100,000万元</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实缴资金</div>
                            <div class="info-value">未知</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">经营地址</div>
                            <div class="info-value">山东省潍坊市临朐县东城街道南环路1777号</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">隶属关系</div>
                            <div class="info-value">无明确隶属关系</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要分支机构</div>
                            <div class="info-value">无明确信息</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-2" class="subsection">
                    <div class="subsection-title">2. 价值度评级</div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                品牌价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>中国建筑铝型材十强企业第三名，产品应用于北京大兴机场、冬奥会速滑馆等国内外标志性工程，与恒大、万科等头部房企及中车、比亚迪等车企战略合作。<br>
                                <strong>价值点：</strong>品牌影响力强，行业地位高，技术实力雄厚。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                收益价值评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>2023年营业收入为152.24亿元，是全国铝型材行业的龙头企业之一，具有较大的市场贡献潜力。<br>
                                <strong>价值点：</strong>营收规模大，具备较高的合作价值和业务拓展空间。
                            </div>
                        </div>
                    </div>

                    <div class="rating-card">
                        <div class="content">
                            <div class="rating-title">
                                合作紧密度评级
                                <span class="rating-stars">★★★☆☆</span>
                            </div>
                            <div class="rating-details">
                                <strong>依据：</strong>与多家知名企业如魏桥创业集团、创新集团有长期合作，参与滨州高端铝材产业园建设，履约能力强。<br>
                                <strong>价值点：</strong>合作经验丰富，具备较强的项目执行能力和客户信任度。
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-3" class="subsection">
                    <div class="subsection-title">3. 经营画像</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">主营业务范围及核心产品/服务</div>
                            <div class="info-value">有色金属合金制造与销售；有色金属压延加工；门窗制造、加工、销售及工程施工</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产业链定位</div>
                            <div class="info-value">上游原材料采购、中游加工制造、下游终端应用（建筑、汽车、新能源等领域）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收入规模</div>
                            <div class="info-value">2023年营业收入为152.24亿元，2024年位列"中国制造业民营企业500强"第488位</div>
                        </div>
                    </div>

                    <div class="btn-group">
                        <span class="btn btn-success">区域重点产业集群</span>
                        <span class="btn btn-success">规上企业</span>
                        <span class="btn btn-success">专精特新企业</span>
                        <span class="btn btn-success">省级单项冠军</span>
                    </div>
                </div>

                <div id="sub1-4" class="subsection">
                    <div class="subsection-title">4. 行业画像</div>

                    <div class="card">
                        <div class="card-title">行业地位</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90a4;">中国建筑铝型材十强企业第三名（2023年），年产能80万吨，国内大型铝型材生产商之一</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #5ba3b4;">国家级工业设计中心、CNAS认可实验室，参与制定60+项国家/行业标准</li>
                            <li style="margin: 8px 0; padding: 8px 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #6b73a0;">产品应用于北京大兴机场、冬奥场馆等国家级工程，合作房企（万科、恒大）及车企（比亚迪、中车）</li>
                        </ul>
                    </div>

                    <div class="card">
                        <div class="card-title">荣誉奖项</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">2025年6月入选山东省"十强"产业集群领军企业</span>
                            <span class="btn btn-success">2025年5月获山东省建设科技创新成果一等奖</span>
                            <span class="btn btn-warning">2025年2月获临朐县经济发展突出贡献奖</span>
                            <span class="btn btn-info">十佳工业企业</span>
                            <span class="btn btn-secondary">外贸进出口先进企业</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">资质认证</div>
                            <div class="info-value">
                                <span class="btn btn-info">规上企业</span>
                                <span class="btn btn-info">专精特新企业</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专利商标统计情况</div>
                            <div class="info-value">
                                累计授权专利：<strong>1600余项</strong><br>
                                2024年新增：<strong>53项</strong>（含发明专利、外观设计）
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-5" class="subsection">
                    <div class="subsection-title">5. 关键人</div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div class="person-card">
                            <div class="person-name">吴维光</div>
                            <div class="person-role">董事长、执行董事</div>
                            <div class="person-desc">负责集团战略决策及重大项目合作（如滨州高端铝材产业园）</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">张连太</div>
                            <div class="person-role">法定代表人、总经理</div>
                            <div class="person-desc">负责公司日常经营及全面管理</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">吴玉萍</div>
                            <div class="person-role">党委副书记、副总裁</div>
                            <div class="person-desc">负责财务、人力资源、效能、行政、企业战略转型</div>
                        </div>

                        <div class="person-card">
                            <div class="person-name">麦树真</div>
                            <div class="person-role">监事</div>
                            <div class="person-desc">负责公司治理监督</div>
                        </div>
                    </div>

                    <div class="card" style="margin-top: 20px;">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">实控人社会角色</div>
                                <div class="info-value">吴维光为公司创始人，拥有27家企业投资背景，持股50%</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">母公司画像</div>
                                <div class="info-value">无明确信息</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">对口业务部门负责人</div>
                                <div class="info-value">未提供具体信息</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sub1-6" class="subsection">
                    <div class="subsection-title">6. 主管单位</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">所属主要行业主管条线</div>
                            <div class="info-value">制造业（有色金属冶炼和压延加工业）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">主要主管单位</div>
                            <div class="info-value">无明确信息</div>
                        </div>
                    </div>
                </div>

                <div id="sub1-7" class="subsection">
                    <div class="subsection-title">7. 重点项目</div>

                    <div class="card">
                        <div class="card-title">（1）行业标杆项目</div>
                        <div class="btn-group">
                            <span class="btn btn-primary">北京大兴机场应用项目</span>
                            <span class="btn btn-info">冬奥会速滑馆等国家级工程</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（2）近期重点项目</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>状态</th>
                                        <th>效果</th>
                                        <th>合作方</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>年产15万吨高性能特种铝材智能工厂</td>
                                        <td><span class="btn btn-success">建成投产</span></td>
                                        <td>32条全自动化生产线，生产效率提升40%</td>
                                        <td>自建</td>
                                    </tr>
                                    <tr>
                                        <td>滨州高端铝材精深加工产业园</td>
                                        <td><span class="btn btn-warning">在建</span></td>
                                        <td>目标降低供应链成本40%</td>
                                        <td>魏桥创业集团、创新集团</td>
                                    </tr>
                                    <tr>
                                        <td>再生铝分选与智能配料系统</td>
                                        <td><span class="btn btn-warning">实施中</span></td>
                                        <td>支持年产30万吨再生铝项目</td>
                                        <td>天力股份</td>
                                    </tr>
                                    <tr>
                                        <td>智能物流系统升级（省级示范）</td>
                                        <td><span class="btn btn-success">建成</span></td>
                                        <td>数据大屏实时调度，搬运效率提升30%</td>
                                        <td>自建</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">（3）主要DICT项目</div>
                        <div style="padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>说明：</strong>未明确提及具体DICT项目，但涉及智能制造、数字化转型相关项目。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 政策信息 -->
            <div id="section2" class="section" style="display: none;">
                <div class="section-title">二、政策信息（与企业关联紧密的相关政策）</div>

                <div class="subsection">
                    <div class="subsection-title">潍坊区域重点政策</div>

                    <div class="card">
                        <div class="card-title">关于加快高新技术企业发展的实施意见</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">潍科发〔2024〕12号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">潍坊市科学技术局</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2024年9月12日</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-info">高新技术企业发展支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #4a90a4;">
                            <strong>主要内容：</strong>鼓励高新技术企业发展，提供税收优惠、审批绿色通道等支持。
                        </div>
                    </div>
                </div>

                <div class="subsection">
                    <div class="subsection-title">省级政策</div>

                    <div class="card">
                        <div class="card-title">山东省"十强"产业集群领军企业培育政策</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政办字〔2025〕124号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省新旧动能转换综合试验区建设领导小组</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2025年6月</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-success">产业集群领军企业培育</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #5ba3b4;">
                            <strong>主要内容：</strong>培育新材料、高端装备等产业集群领军企业。
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">山东省制造业数字化转型提标行动方案</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">文号</div>
                                <div class="info-value">鲁政发〔2023〕12号</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布单位</div>
                                <div class="info-value">山东省政府</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">发布日期</div>
                                <div class="info-value">2023年</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">政策类型</div>
                                <div class="info-value"><span class="btn btn-warning">数字化转型支持</span></div>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #6b73a0;">
                            <strong>主要内容：</strong>推动制造业数字化转型，提升智能化水平。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行业热点 -->
            <div id="section3" class="section" style="display: none;">
                <div class="section-title">三、行业热点</div>

                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>热点方向</th>
                                    <th>案例</th>
                                    <th>效果</th>
                                    <th>潜在商机</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="btn btn-primary">智能制造</span></td>
                                    <td>年产15万吨高性能特种铝材智能工厂</td>
                                    <td>生产效率提升40%，人工成本降低25%</td>
                                    <td>工业互联网平台、AI管控系统</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-success">绿色低碳</span></td>
                                    <td>再生铝分选与智能配料系统</td>
                                    <td>支持碳足迹追踪与绿色制造认证</td>
                                    <td>再生铝循环利用、氢能技术</td>
                                </tr>
                                <tr>
                                    <td><span class="btn btn-warning">数字化转型</span></td>
                                    <td>智能物流系统升级（省级示范）</td>
                                    <td>提升搬运效率30%</td>
                                    <td>数据驱动的精益管理</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 招投标信息 -->
            <div id="section4" class="section" style="display: none;">
                <div class="section-title">四、政采公示/招投标信息</div>

                <div class="subsection">
                    <div class="card" style="text-align: center; padding: 60px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                        <div style="font-size: 48px; color: #6c757d; margin-bottom: 16px;">📋</div>
                        <div style="font-size: 18px; color: #6c757d; font-weight: 500;">暂无相关信息</div>
                        <div style="font-size: 14px; color: #adb5bd; margin-top: 8px;">当前没有可显示的政采公示或招投标信息</div>
                    </div>
                </div>
            </div>

            <!-- 商机类型 -->
            <div id="section5" class="section" style="display: none;">
                <div class="section-title">五、商机类型</div>

                <div class="subsection">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>预算额度</th>
                                    <th>启动周期</th>
                                    <th>效果目标</th>
                                    <th>商机可能性评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">年产15万吨高性能特种铝材智能工厂</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>16亿元</td>
                                    <td>2019年立项</td>
                                    <td>生产效率提升40%</td>
                                    <td><span class="score-badge score-high">0.98</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">滨州高端铝材精深加工产业园</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>联合投资（含1700亩土地资源）</td>
                                    <td>2025年新立项</td>
                                    <td>降低供应链成本40%</td>
                                    <td><span class="score-badge score-high">0.95</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">再生铝分选与智能配料系统</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>千万级投资</td>
                                    <td>2025年立项</td>
                                    <td>支持年产30万吨再生铝项目及碳足迹追踪</td>
                                    <td><span class="score-badge score-high">0.93</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: #1d1d1f; margin-bottom: 4px;">智能物流系统升级（省级示范）</div>
                                        <span class="btn btn-success" style="font-size: 10px;">显性商机</span>
                                    </td>
                                    <td>省级专项资金支持</td>
                                    <td>2024-2025年</td>
                                    <td>提升搬运效率30%</td>
                                    <td><span class="score-badge score-high">0.96</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, element) {
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.display = 'none';
            });

            document.getElementById(sectionId).style.display = 'block';

            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');

            const navSecondary = document.getElementById('nav-secondary');
            if (sectionId === 'section1') {
                navSecondary.classList.add('show');
            } else {
                navSecondary.classList.remove('show');
            }

            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                const navHeight = document.querySelector('.nav-container').offsetHeight;
                const elementPosition = element.offsetTop - navHeight - 20;
                window.scrollTo({
                    top: elementPosition,
                    behavior: 'smooth'
                });
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.display = index === 0 ? 'block' : 'none';
            });
            document.getElementById('nav-secondary').classList.add('show');

            document.querySelectorAll('.card, .info-item, .person-card').forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
