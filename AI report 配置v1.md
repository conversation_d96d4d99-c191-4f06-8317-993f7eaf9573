# AI Report 配置 v1.0

## 设计理念
基于苹果设计语言的企业画像报告模板，注重简洁、现代、专业的视觉呈现。

## 颜色方案
### 主色调系统
- **主色调**: #4a90a4 (蓝绿色)
- **辅助色1**: #5ba3b4 (浅蓝绿)
- **辅助色2**: #6b73a0 (蓝紫色)
- **辅助色3**: #7b83b0 (浅蓝紫)
- **辅助色4**: #8b93c0 (淡蓝紫)
- **辅助色5**: #8b5a9c (紫色)

### 功能色彩
- **成功色**: #5ba3b4
- **警告色**: #6b73a0
- **信息色**: #7b83b0
- **次要色**: #8b93c0

## 字体系统
```css
font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
```

## 布局规范
### 容器设置
- **最大宽度**: 1400px (适配14寸MacBook)
- **边距**: 20px (响应式)
- **圆角**: 20px (主容器), 16px (卡片), 12px (小元素)

### 间距系统
- **大间距**: 40px (章节间)
- **中间距**: 30px (子章节间)
- **小间距**: 20px (元素间)
- **微间距**: 16px (内容间)

## 组件规范

### 导航系统
#### 主导航
- **背景**: rgba(255, 255, 255, 0.8) + backdrop-filter: blur(20px)
- **激活状态**: 主色调背景 + 白色文字
- **悬停效果**: translateY(-1px)

#### 二级导航
- **显示条件**: 仅在"战客动态"章节显示
- **背景**: 主色调5%透明度
- **文字颜色**: 主色调

### 卡片系统
#### 基础卡片
```css
background: white;
border-radius: 16px;
padding: 24px;
box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
border: 1px solid rgba(0, 0, 0, 0.05);
```

#### 评级卡片
- **渐变背景**: 使用主色调系统的渐变组合
- **毛玻璃效果**: backdrop-filter: blur(10px)
- **星级评分**: 金色 #ffd700

#### 人员卡片
- **背景**: 多色渐变 (主色调 → 辅助色2 → 辅助色5)
- **装饰元素**: 右上角半透明圆形

### 按钮系统
#### 基础样式
```css
padding: 8px 16px;
border-radius: 20px;
font-size: 12px;
font-weight: 500;
transition: all 0.3s ease;
```

#### 颜色分类
- **主要按钮**: #4a90a4
- **成功按钮**: #5ba3b4
- **警告按钮**: #6b73a0
- **信息按钮**: #7b83b0
- **次要按钮**: #8b93c0

### 表格系统
#### 样式规范
- **头部背景**: #f8f9fa
- **边框**: #e9ecef (头部), #f2f2f7 (行间)
- **悬停效果**: #f8f9fa 背景
- **圆角容器**: 12px

### 信息网格
#### 布局
```css
display: grid;
grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
gap: 16px;
```

#### 信息项样式
- **背景**: #f8f9fa
- **圆角**: 12px
- **悬停效果**: translateY(-2px) + 阴影增强

## 动画效果

### 页面加载
```css
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### 交互反馈
- **悬停**: translateY(-2px) + 阴影变化
- **点击**: 涟漪效果 (0.6s)
- **过渡**: all 0.3s ease

## 响应式设计

### 断点设置
- **桌面**: > 1440px
- **笔记本**: 768px - 1440px
- **移动端**: < 768px

### 适配规则
#### 1440px以下
```css
.container {
    margin: 20px;
    max-width: calc(100% - 40px);
}
```

#### 768px以下
- 导航项字体: 12px
- 导航内边距: 6px 12px
- 标题字体: 24px
- 信息网格: 单列布局

## 内容结构

### 标准章节
1. **战客动态** (含7个子章节)
   - 基础信息
   - 价值度评级
   - 经营画像
   - 行业画像
   - 关键人
   - 主管单位
   - 重点项目

2. **政策信息**
3. **行业热点**
4. **招投标信息**
5. **商机类型**

### 特殊处理
- **空状态**: 使用图标 + 说明文字
- **评分显示**: 彩色徽章 + 数值
- **状态标签**: 不同颜色区分状态

## 技术实现

### CSS特性
- **毛玻璃效果**: backdrop-filter: blur(20px)
- **渐变背景**: linear-gradient(135deg, ...)
- **自定义滚动条**: webkit-scrollbar 样式
- **平滑滚动**: scroll-behavior: smooth

### JavaScript功能
- **章节切换**: showSection()
- **平滑滚动**: scrollToElement()
- **交互增强**: 悬停效果、点击反馈
- **初始化**: DOMContentLoaded 事件

## 文件命名规范
- **基础版本**: [企业名称]-企业画像报告.html
- **时间戳版本**: [企业名称]-企业画像报告_[时分].html
- **配置文件**: AI report 配置v[版本号].md

## 数据准确性规范

### 数据来源分类
#### ✅ 原文档直接数据
- **投资金额**: 必须来自原文明确数字
- **时间节点**: 必须来自原文明确表述
- **项目名称**: 必须与原文完全一致
- **服务内容**: 必须基于原文技术规格

#### ⚠️ 合理推测数据
- **效果目标**: 基于项目性质的行业标准推测
- **商机评分**: 基于项目规模和重要性评估
- **项目分类**: 显性/准显性/预判商机分类
- **必须标注**: 推测性质，避免误导

#### ❌ 禁止添加数据
- **具体时间段**: 如"2025.01-2026.12"（原文未明确时）
- **精确预算**: 如"约XX万元"（原文未提及时）
- **虚构联系方式**: 电话、邮箱等敏感信息
- **未验证的技术规格**: 带宽、容量等具体参数

### 质量检查流程

#### 第一步：完整性检查
```
□ 原文档是否完整提取？
□ 所有章节是否都已处理？
□ 表格行数是否与原文一致？
□ 文档末尾内容是否遗漏？
```

#### 第二步：数据准确性验证
```
□ 所有数字是否来自原文？
□ 时间信息是否准确标注？
□ "未明确"状态是否正确保留？
□ 推测信息是否合理标注？
```

#### 第三步：结构完整性检查
```
□ 商机类型数量是否完整？
□ 表格结构是否与原文匹配？
□ 关键信息是否有遗漏？
□ 跨页内容是否完整处理？
```

#### 第四步：最终验证
```
□ 逐项对比原文档
□ 检查容易遗漏的部分
□ 验证数据源标注
□ 确认无虚构信息
```

### 常见问题及避免方法

#### 🚨 容易遗漏的部分
- **文档末尾**: 观测商机、补充信息
- **表格最后几行**: 特别是商机类型表格
- **附录内容**: 政策文件、招投标信息
- **跨页内容**: 长表格的后续部分

#### 🛠️ 改进措施
1. **分段处理**: 将长文档分成小段逐一处理
2. **双重验证**: 处理完后再次完整检查
3. **标记进度**: 明确标记已处理和未处理部分
4. **交叉验证**: 转换后与原文逐项对比

#### 📋 检查清单模板
```
原文档信息统计：
- 商机总数: __ 个
- 表格总行数: __ 行
- 主要章节数: __ 个
- 关键数据点: __ 个

转换结果验证：
- 商机数量: __ 个 ✅/❌
- 表格行数: __ 行 ✅/❌
- 章节完整: ✅/❌
- 数据准确: ✅/❌
```

## 质量标准
- ✅ 无横向滚动 (14寸MacBook)
- ✅ 完整内容保留 (无遗漏)
- ✅ 数据准确性 (来源可追溯)
- ✅ 专业视觉设计
- ✅ 流畅交互体验
- ✅ 响应式适配
- ✅ 代码简洁清晰

## 强化质量控制流程 (v2.0)

### 🚨 强制性检查流程

#### 阶段一：信息提取完整性验证
**必须在开始转换前完成**
1. **原文档全文解析**：
   ```
   □ 逐行读取原文档内容
   □ 建立信息提取清单
   □ 统计各类信息数量
   □ 标记重要数据位置
   ```

2. **关键信息统计**：
   ```
   □ 商机总数：___ 个
   □ 政策文件数：___ 个
   □ 荣誉奖项数：___ 个
   □ DICT项目数：___ 个
   □ 关键人员数：___ 个
   □ 行业热点数：___ 个
   ```

#### 阶段二：逐章节验证机制
**每完成一个章节立即执行**

1. **基础信息验证**：
   ```
   □ 企业名称：完全一致
   □ 成立日期：格式和内容准确
   □ 注册资金：数字和单位准确
   □ 经营地址：完整准确
   □ 隶属关系：表述准确
   ```

2. **价值度评级验证**：
   ```
   □ 评级星级：与原文一致
   □ 评级依据：完整引用原文
   □ 价值点：准确概括
   □ 具体数据：如营收数字必须准确
   ```

3. **行业画像验证**：
   ```
   □ 行业地位：所有要点都包含
   □ 荣誉奖项：逐项核对，确保无遗漏
   □ 专利数量：数字准确
   □ 市场地位：排名和份额准确
   ```

#### 阶段三：最终完整性检查
**转换完成后强制执行**

1. **数量核对**：
   ```
   □ 商机数量：原文 ___ 个 = HTML ___ 个 ✅
   □ 政策数量：原文 ___ 个 = HTML ___ 个 ✅
   □ 荣誉数量：原文 ___ 个 = HTML ___ 个 ✅
   □ 项目数量：原文 ___ 个 = HTML ___ 个 ✅
   ```

2. **内容完整性**：
   ```
   □ 每个荣誉奖项都已包含
   □ 每个政策文件都已包含
   □ 每个DICT项目都已包含
   □ 每个商机都已包含
   □ 所有关键数据都已包含
   ```

### 🔍 错误预防机制

#### 信息遗漏预防
1. **使用检查清单**：每个信息类别建立独立清单
2. **分段验证**：每50行内容进行一次核对
3. **交叉引用**：同一信息在不同位置的一致性检查

#### 数据错误预防
1. **原文档唯一参考**：不依赖记忆或推测
2. **数字格式统一**：金额、时间、数量的表述格式
3. **术语准确性**：专业术语和企业名称的准确性

### 📋 详细检查清单模板

#### 战客动态章节
```
基础信息 (8项):
□ 企业名称 □ 成立日期 □ 企业属性 □ 注册资金
□ 实缴资金 □ 经营地址 □ 隶属关系 □ 分支机构

价值度评级 (3项):
□ 品牌价值评级 □ 收益价值评级 □ 合作紧密度评级

经营画像 (3项):
□ 主营业务范围 □ 产业链定位 □ 收入规模

行业画像:
□ 行业地位 (所有要点) □ 荣誉奖项 (逐项核对) □ 专利情况

关键人:
□ 所有人员姓名 □ 职务准确 □ 职责描述 □ 其他信息

重点项目:
□ 标杆项目 □ 近期项目 □ DICT项目 (逐项核对)
```

#### 其他章节
```
政策信息:
□ 按地域正确分类 □ 文件名称完整 □ 发布单位准确

行业热点:
□ 热点方向 □ 案例描述 □ 效果说明 □ 潜在商机

商机类型:
□ 商机数量准确 □ 评分正确 □ 分类准确 □ 描述完整
```

### ⚠️ 质量红线
**以下情况必须停止并重新检查**：
1. 发现任何信息遗漏
2. 发现数据不一致
3. 无法在原文档中找到信息来源
4. 数量统计不匹配

### 🎯 质量目标
- **完整性**: 100% (无任何遗漏)
- **准确性**: 100% (关键数据零错误)
- **一致性**: 100% (同一信息保持一致)
- **可追溯性**: 100% (每个信息点都能追溯到原文)
