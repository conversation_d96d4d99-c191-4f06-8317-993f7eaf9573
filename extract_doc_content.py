#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取山东新和成控股文档内容
"""

import docx
import sys
import os

def extract_document_content():
    doc_path = "0715  战客动态2家/山东新和成控股有限公司-战客动态.docx"
    
    if not os.path.exists(doc_path):
        print(f"文件不存在: {doc_path}")
        return
    
    try:
        doc = docx.Document(doc_path)
        
        print("=== 山东新和成控股有限公司文档内容提取 ===")
        print()
        
        # 提取所有段落
        paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                paragraphs.append({
                    'index': i + 1,
                    'text': text
                })
        
        print(f"总段落数: {len(paragraphs)}")
        print()
        
        # 输出所有段落内容
        print("=== 完整段落内容 ===")
        for para in paragraphs:
            print(f"{para['index']:3d}: {para['text']}")
        
        print()
        print("=== 表格内容 ===")
        
        # 提取表格
        for i, table in enumerate(doc.tables):
            print(f"\n表格 {i+1} ({len(table.rows)}行 x {len(table.columns) if table.rows else 0}列):")
            for j, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_data.append(cell_text)
                print(f"  行{j+1}: {' | '.join(row_data)}")
        
        print()
        print("=== 基础信息查找 ===")
        
        # 查找基础信息
        basic_keywords = ['企业名称', '成立日期', '注册资金', '经营地址', '企业属性', '实缴资金', '隶属关系', '分支机构']
        
        for keyword in basic_keywords:
            print(f"\n查找关键词: {keyword}")
            found = False
            for para in paragraphs:
                if keyword in para['text']:
                    print(f"  {para['index']:3d}: {para['text']}")
                    found = True
            if not found:
                print(f"  未找到包含'{keyword}'的段落")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_document_content()
