<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>潍柴动力股份有限公司企业画像报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5; /* Light gray background, Apple-like */
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            display: flex; /* Use flexbox for main layout */
            min-height: 100vh; /* Full viewport height */
            overflow: hidden; /* Prevent body scroll, manage content scroll internally */
        }
        .sidebar {
            width: 240px; /* Fixed width sidebar */
            background-color: #ffffff;
            padding: 2rem 1.5rem;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05); /* Subtle shadow */
            display: flex;
            flex-direction: column;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto; /* Allow sidebar to scroll if content is long */
            flex-shrink: 0; /* Prevent sidebar from shrinking */
        }
        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #e2e8f0;
        }
        .sidebar nav ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .sidebar nav ul li {
            margin-bottom: 0.5rem;
        }
        .sidebar nav ul li a {
            display: block;
            padding: 0.75rem 1rem;
            color: #4a5568;
            text-decoration: none;
            border-radius: 8px;
            transition: background-color 0.2s ease, color 0.2s ease;
            font-weight: 500;
        }
        .sidebar nav ul li a:hover {
            background-color: #e6f0fa; /* Light blue hover */
            color: #2c5282;
        }
        .sidebar nav ul li a.active {
            background-color: #dbeafe; /* Stronger blue for active */
            color: #2c5282;
            font-weight: 600;
        }

        .main-content {
            flex-grow: 1; /* Main content takes remaining space */
            padding: 2rem;
            overflow-y: auto; /* Allow main content to scroll vertically */
            max-height: 100vh; /* Ensure it doesn't overflow viewport */
        }
        .report-header {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a202c;
            text-align: center;
            margin-bottom: 2.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #edf2f7;
        }
        .section-card {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06); /* Softer shadow */
            margin-bottom: 1.5rem;
            overflow: hidden; /* Ensure rounded corners apply to content */
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem 1.5rem;
            background-color: #f7f9fb; /* Slightly off-white header */
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            font-size: 1.75rem; /* Adjusted for better fit */
            font-weight: 600;
            color: #2d3748;
            transition: background-color 0.2s ease;
        }
        .section-header:hover {
            background-color: #eff3f7;
        }
        .section-header .arrow {
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }
        .section-header.collapsed .arrow {
            transform: rotate(-90deg);
        }
        .section-content {
            padding: 1.5rem;
            display: none; /* Hidden by default */
            transition: max-height 0.3s ease-out, padding 0.3s ease-out; /* Smooth transition for collapse */
            overflow: hidden; /* Hide overflow during transition */
        }
        .section-content.expanded {
            display: block; /* Show when expanded */
        }

        h3 {
            font-size: 1.25rem; /* Adjusted for better fit */
            font-weight: 600;
            color: #4a5568;
            margin-top: 1.25rem;
            margin-bottom: 0.75rem;
        }
        p, ul, table {
            margin-bottom: 0.75rem; /* Reduced margin for compactness */
        }
        ul {
            list-style-type: none;
            padding-left: 0;
        }
        ul li {
            background-color: #fcfdfe; /* Lighter background for list items */
            padding: 0.6rem 1rem; /* Slightly reduced padding */
            border-radius: 6px; /* Slightly smaller radius */
            margin-bottom: 0.4rem;
            border: 1px solid #ebf4f5;
            font-size: 0.95rem; /* Slightly smaller font for list items */
        }
        ul li strong {
            color: #2c5282;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04); /* Table specific shadow */
        }
        .table-container {
            overflow-x: auto; /* Allow horizontal scroll for wide tables */
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on touch devices */
        }
        th, td {
            padding: 0.8rem 1rem; /* Reduced padding for table cells */
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.9rem; /* Slightly smaller font for table */
        }
        th {
            background-color: #e6f0fa; /* Light blue header */
            color: #2c5282;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f7fafc;
        }
        tr:hover {
            background-color: #edf2f7;
        }
        .rating {
            color: #f6ad55; /* Star color */
            font-size: 1.1rem; /* Slightly smaller stars */
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-title">报告导航</div>
        <nav>
            <ul>
                <li><a href="#section-1" class="nav-link">一、战客动态</a></li>
                <li><a href="#section-2" class="nav-link">二、政策信息</a></li>
                <li><a href="#section-3" class="nav-link">三、行业热点</a></li>
                <li><a href="#section-4" class="nav-link">四、政采公示/招投标信息</a></li>
                <li><a href="#section-5" class="nav-link">五、商机</a></li>
            </ul>
        </nav>
    </div>

    <div class="main-content">
        <h1 class="report-header">潍柴动力股份有限公司企业画像报告</h1>

        <!-- 一、战客动态 -->
        <div id="section-1" class="section-card">
            <div class="section-header">
                <span>一、战客动态</span>
                <span class="arrow">▼</span>
            </div>
            <div class="section-content expanded">
                <h3>1. 基础信息</h3>
                <ul>
                    <li><strong>企业名称（简称）</strong> ：潍柴动力</li>
                    <li><strong>成立日期</strong> ：2002-12-23</li>
                    <li><strong>企业属性</strong> ：股份有限公司（港澳台投资、上市）</li>
                    <li><strong>注册资金</strong> ：未知</li>
                    <li><strong>实缴资金</strong> ：未知</li>
                    <li><strong>经营地址</strong> ：山东省潍坊市高新区福寿东街197号甲</li>
                    <li><strong>隶属关系</strong> ：山东重工集团</li>
                    <li>
                        <strong>主要分支机构</strong> ：
                        <ul>
                            <li>国内：陕西重型汽车、法士特齿轮、汉德车桥、潍柴雷沃智慧农业</li>
                            <li>境外：德国凯傲集团、林德液压、意大利法拉帝、法国博杜安</li>
                        </ul>
                    </li>
                </ul>

                <h3>2. 价值度评级</h3>
                <ul>
                    <li>
                        <strong>品牌价值</strong>
                        <p>评级 ：<span class="rating">★★★☆☆</span></p>
                        <p>依据 ：全球柴油机龙头，重卡发动机国内市占率38.7%，天然气重卡发动机市占率59.6%，法士特变速器市占率超80%。</p>
                        <p>价值点 ：品牌影响力强，行业地位显著。</p>
                    </li>
                    <li>
                        <strong>收益价值(现有合作业务年贡献值/潜在业务合作规模预测)</strong>
                        <p>评级 ：<span class="rating">★★★☆☆</span></p>
                        <p>依据 ：2024年营业收入2156.91亿元，归母净利润114.03亿元，海外收入占比超35%。</p>
                        <p>价值点 ：营收规模大，盈利能力较强，海外市场拓展潜力大。</p>
                    </li>
                    <li>
                        <strong>合作紧密度(指的是合作年限及续约情况/履约能力评估（历史项目交付质量）)</strong>
                        <p>评级 ：<span class="rating">★★★☆☆</span></p>
                        <p>依据 ：与多家国际企业有长期合作，如德国凯傲集团、林德液压等，且在多个重大项目中表现出色。</p>
                        <p>价值点 ：合作稳定，履约能力强。</p>
                    </li>
                </ul>

                <h3>3. 经营画像</h3>
                <ul>
                    <li><strong>主营业务范围及核心产品/服务有哪些（按营收占比排序）</strong> ：发动机、变速箱、新能源电池、叉车、智能物流设备等。</li>
                    <li><strong>产业链定位</strong> ：上游制造、中游研发、下游销售和服务。</li>
                    <li><strong>收入规模</strong> ：2024年营业收入2156.91亿元。</li>
                    <li><strong>是否所属区域重点产业集群</strong> ：是</li>
                    <li><strong>是否规上企业</strong> ：是</li>
                    <li><strong>是否专精特新企业</strong> ：是</li>
                    <li><strong>是否省级单项冠军</strong> ：是</li>
                </ul>

                <h3>4. 行业画像</h3>
                <ul>
                    <li><strong>行业地位（市场份额/技术专利/标准制定地位）</strong> ：全球柴油机龙头，重卡发动机国内市占率38.7%，天然气重卡发动机市占率59.6%。</li>
                    <li>
                        <strong>荣誉奖项</strong> ：
                        <ul>
                            <li>全国首批“卓越级智能工厂”</li>
                            <li>国家科技进步奖一等奖</li>
                            <li>山东省科学技术进步奖一等奖等</li>
                        </ul>
                    </li>
                    <li><strong>资质认证</strong> ：规上企业、专精特新企业。</li>
                    <li><strong>专利 商标统计情况</strong> ：未明确提供具体数据。</li>
                </ul>

                <h3>5. 关键人</h3>
                <ul>
                    <li>
                        <strong>关键人姓名、职务、职责、联系方式</strong> ：
                        <ul>
                            <li>马常海：董事长、法定代表人，负责公司战略规划。</li>
                            <li>王德成：总经理、首席执行官，负责日常经营管理。</li>
                            <li>黄维彪：执行董事、党委副书记，负责人力资源管理及党建工作。</li>
                            <li>满慎刚：山东重工集团党委书记、董事长，推动数智化转型。</li>
                        </ul>
                    </li>
                    <li><strong>实控人社会角色</strong> ：山东重工集团实际控制人，具有较强的行业影响力。</li>
                    <li><strong>母公司画像</strong> ：山东重工集团，是国内领先的装备制造企业。</li>
                    <li><strong>对口业务部门负责人及对接人信息</strong> ：未明确提供。</li>
                </ul>

                <h3>6. 主管单位</h3>
                <ul>
                    <li><strong>所属主要行业主管条线</strong> ：制造业</li>
                    <li><strong>主要主管单位</strong> ：山东省工业和信息化厅、潍坊市科技局等。</li>
                </ul>

                <h3>7. 重点项目</h3>
                <ul>
                    <li>
                        <strong>（1）行业标杆项目</strong>：
                        <ul>
                            <li>高端发动机数字化工厂，入选“卓越级智能工厂”。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>（2）近期重点项目</strong>：
                        <ul>
                            <li>潍柴弗迪新能源产业园 ：总投资560亿元，一期2024年3月投产。</li>
                            <li>氢燃料重卡示范运营 ：累计超100万公里。</li>
                            <li>矿山服务2.0数字化平台 ：提升运营效率30%。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>（3）主要DICT项目</strong>：
                        <ul>
                            <li>高端发动机数字化工厂、矿山服务2.0数字化平台、智能物流系统等。</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 二、政策信息(与企业关联紧密的相关政策) -->
        <div id="section-2" class="section-card">
            <div class="section-header">
                <span>二、政策信息(与企业关联紧密的相关政策)</span>
                <span class="arrow">▼</span>
            </div>
            <div class="section-content">
                <h3>潍坊区域重点政策</h3>
                <ul>
                    <li><strong>文件名称</strong> ：关于印发《关于加快高新技术企业发展的实施意见》的通知</li>
                    <li><strong>文号</strong> ：未提供</li>
                    <li><strong>发布单位</strong> ：潍坊市科学技术局</li>
                    <li><strong>发布日期</strong> ：2024年9月12日</li>
                    <li><strong>政策链接</strong> ：未提供</li>
                    <li><strong>政策类型</strong> ：高新技术企业发展支持</li>
                    <li><strong>主要内容</strong> ：支持高新技术企业培育，强化科技创新能力，推动产业转型升级。</li>
                </ul>

                <h3>国家级政策</h3>
                <ul>
                    <li><strong>文件名称</strong> ：《“十四五”智能制造发展规划》</li>
                    <li><strong>文号</strong> ：工信部等八部门联合发布</li>
                    <li><strong>发布单位</strong> ：工业和信息化部</li>
                    <li><strong>发布日期</strong> ：2021年12月</li>
                    <li><strong>政策链接</strong> ：未提供</li>
                    <li><strong>政策类型</strong> ：智能制造发展</li>
                    <li><strong>主要内容</strong> ：引导智能工厂梯度培育，支持企业数字化转型。</li>
                </ul>

                <h3>省级政策</h3>
                <ul>
                    <li>
                        <strong>文件名称</strong> ：《山东省集中式风电光伏发电项目竞争性配置工作管理办法（试行）》
                        <ul>
                            <li><strong>文号</strong> ：未提供</li>
                            <li><strong>发布单位</strong> ：山东省发展和改革委员会、山东省能源局</li>
                            <li><strong>发布日期</strong> ：未提供</li>
                            <li><strong>政策链接</strong> ：未提供</li>
                            <li><strong>政策类型</strong> ：新能源项目开发</li>
                            <li><strong>主要内容</strong> ：规范风电、光伏项目竞争性配置，推动新能源产业发展。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>文件名称</strong> ：《山东省科技创新引领标志性产业链高质量发展实施方案（2024—2027年）》
                        <ul>
                            <li><strong>文号</strong> ：未提供</li>
                            <li><strong>发布单位</strong> ：山东省人民政府办公厅</li>
                            <li><strong>发布日期</strong> ：2024年</li>
                            <li><strong>政策链接</strong> ：未提供</li>
                            <li><strong>政策类型</strong> ：科技创新与产业升级</li>
                            <li><strong>主要内容</strong> ：推动科技创新引领标志性产业链高质量发展，重点支持高端装备、新能源等领域。</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 三、行业热点 -->
        <div id="section-3" class="section-card">
            <div class="section-header">
                <span>三、行业热点</span>
                <span class="arrow">▼</span>
            </div>
            <div class="section-content">
                <ul>
                    <li><strong>热点方向</strong> ：智能制造、新能源、绿色低碳、数字化转型。</li>
                    <li>
                        <strong>案例</strong> ：
                        <ul>
                            <li>高端发动机数字化工厂，加工自动化率100%。</li>
                            <li>潍柴弗迪新能源产业园，一期20GWh产能投产。</li>
                        </ul>
                    </li>
                    <li>
                        <strong>效果</strong> ：
                        <ul>
                            <li>生产效率提升，成本降低，市场竞争力增强。</li>
                        </ul>
                    </li>
                    <li><strong>潜在商机</strong> ：新能源电池、智能物流、矿山服务等领域的进一步拓展。</li>
                    <li><strong>信息来源</strong> ：潍柴动力2024年年报、中国机械工业联合会报告。</li>
                </ul>
            </div>
        </div>

        <!-- 四、政采公示/招投标信息 -->
        <div id="section-4" class="section-card">
            <div class="section-header">
                <span>四、政采公示/招投标信息</span>
                <span class="arrow">▼</span>
            </div>
            <div class="section-content">
                <p>无</p>
            </div>
        </div>

        <!-- 五、商机 -->
        <div id="section-5" class="section-card">
            <div class="section-header">
                <span>五、商机</span>
                <span class="arrow">▼</span>
            </div>
            <div class="section-content">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>类型</th>
                                <th>项目名称</th>
                                <th>预算额度</th>
                                <th>启动周期</th>
                                <th>效果目标</th>
                                <th>商机可能性评分</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>准显性商机</td>
                                <td>潍柴弗迪新能源产业园</td>
                                <td>560亿元</td>
                                <td>2024年3月</td>
                                <td>一期20GWh产能投产，电池良率99.5%</td>
                                <td>0.90</td>
                            </tr>
                            <tr>
                                <td>准显性商机</td>
                                <td>SRM供应商管理系统升级</td>
                                <td>未披露</td>
                                <td>2025年7月</td>
                                <td>采购效率提升40%，整合3套原有系统</td>
                                <td>0.80</td>
                            </tr>
                            <tr>
                                <td>预判商机</td>
                                <td>潍柴高端发动机数字化工厂</td>
                                <td>未披露</td>
                                <td>2025年</td>
                                <td>加工自动化率100%，入选“卓越级智能工厂”</td>
                                <td>0.92</td>
                            </tr>
                            <tr>
                                <td>预判商机</td>
                                <td>潍柴弗迪新能源产业园</td>
                                <td>560亿元</td>
                                <td>2024年3月</td>
                                <td>一期20GWh产能投产，电池良率99.5%</td>
                                <td>0.90</td>
                            </tr>
                            <tr>
                                <td>预判商机</td>
                                <td>智能农机数字工厂</td>
                                <td>未披露</td>
                                <td>2025年</td>
                                <td>建设新能源大功率拖拉机生产线</td>
                                <td>0.85</td>
                            </tr>
                            <tr>
                                <td>预判商机</td>
                                <td>5G+工业互联网改造</td>
                                <td>未披露</td>
                                <td>2025年</td>
                                <td>申请最高100万元奖补，扩展10个以上智能工厂改造项目</td>
                                <td>0.87</td>
                            </tr>
                            <tr>
                                <td>潜在商机</td>
                                <td>智能物流园区建设</td>
                                <td>未披露</td>
                                <td>2025年</td>
                                <td>借助'山东科技大市场'平台，建设德马泰克自动化仓储系统升级项目</td>
                                <td>0.85</td>
                            </tr>
                            <tr>
                                <td>潜在商机</td>
                                <td>工业机器人研发项目</td>
                                <td>未披露</td>
                                <td>2025年</td>
                                <td>开发核电、石化场景特种机器人</td>
                                <td>0.82</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sectionHeaders = document.querySelectorAll('.section-header');
            const navLinks = document.querySelectorAll('.nav-link');

            // Function to toggle section visibility
            function toggleSection(header) {
                const content = header.nextElementSibling;
                const arrow = header.querySelector('.arrow');

                if (content.classList.contains('expanded')) {
                    content.classList.remove('expanded');
                    arrow.style.transform = 'rotate(-90deg)';
                } else {
                    content.classList.add('expanded');
                    arrow.style.transform = 'rotate(0deg)';
                }
            }

            // Set up click listeners for section headers
            sectionHeaders.forEach(header => {
                header.addEventListener('click', () => {
                    toggleSection(header);
                });
            });

            // Handle navigation clicks for smooth scrolling and active state
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault(); // Prevent default anchor jump

                    const targetId = this.getAttribute('href').substring(1);
                    const targetSectionCard = document.getElementById(targetId);
                    const targetContent = targetSectionCard.querySelector('.section-content');
                    const targetHeader = targetSectionCard.querySelector('.section-header');

                    // Expand the target section if it's collapsed
                    if (!targetContent.classList.contains('expanded')) {
                        toggleSection(targetHeader);
                    }

                    // Scroll to the target section
                    targetSectionCard.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update active class for navigation links
                    navLinks.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Observe sections for active navigation link on scroll
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.5 // Trigger when 50% of the section is visible
            };

            const sectionObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const id = entry.target.id;
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${id}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.section-card').forEach(section => {
                sectionObserver.observe(section);
            });

            // Initially set the first nav link as active and expand the first section
            if (navLinks.length > 0) {
                navLinks[0].classList.add('active');
            }
            if (sectionHeaders.length > 0) {
                sectionHeaders[0].click(); // Simulate click to expand the first section
            }
        });
    </script>
</body>
</html>
